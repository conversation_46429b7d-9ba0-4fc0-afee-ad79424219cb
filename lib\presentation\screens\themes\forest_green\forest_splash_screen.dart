import 'package:flutter/material.dart';

class ForestSplashScreen extends StatelessWidget {
  const ForestSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF228B22),
              Color(0xFF32CD32),
              Color(0xFF90EE90),
            ],
          ),
        ),
        child: const Center(
          child: Text(
            'Forest Green Theme',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
