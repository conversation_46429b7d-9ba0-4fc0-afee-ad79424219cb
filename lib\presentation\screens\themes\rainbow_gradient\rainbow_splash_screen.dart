import 'package:flutter/material.dart';

class RainbowSplashScreen extends StatelessWidget {
  const RainbowSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFF0000),
              Color(0xFFFF8000),
              Color(0xFFFFFF00),
              Color(0xFF00FF00),
              Color(0xFF0000FF),
              Color(0xFF8000FF),
            ],
          ),
        ),
        child: const Center(
          child: Text(
            'Rainbow Gradient Theme',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(2, 2),
                  blurRadius: 4,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
