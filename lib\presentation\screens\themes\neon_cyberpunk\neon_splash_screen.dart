import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:shimmer/shimmer.dart';
import 'neon_home_screen.dart';

class NeonSplashScreen extends StatefulWidget {
  const NeonSplashScreen({super.key});

  @override
  State<NeonSplashScreen> createState() => _NeonSplashScreenState();
}

class _NeonSplashScreenState extends State<NeonSplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particleController;
  late AnimationController _glitchController;

  @override
  void initState() {
    super.initState();
    
    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    _glitchController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    )..repeat(reverse: true);

    _startSplashSequence();
  }

  void _startSplashSequence() async {
    await Future.delayed(const Duration(milliseconds: 500));
    _mainController.forward();
    
    await Future.delayed(const Duration(seconds: 4));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const NeonHomeScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOutCubic,
              )),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 1000),
        ),
      );
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particleController.dispose();
    _glitchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF0A0A0A),
              Color(0xFF1A0A1A),
              Color(0xFF0A1A1A),
              Color(0xFF000000),
            ],
          ),
        ),
        child: Stack(
          children: [
            // Animated Grid Background
            ...List.generate(20, (index) {
              return AnimatedBuilder(
                animation: _particleController,
                builder: (context, child) {
                  final progress = (_particleController.value + index * 0.1) % 1.0;
                  final x = (index % 5) * (size.width / 4);
                  final y = (index ~/ 5) * (size.height / 4);
                  final opacity = (math.sin(progress * 2 * math.pi) + 1) / 2 * 0.3;
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Container(
                      width: 2,
                      height: size.height,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Color(0xFF00FFFF).withOpacity(opacity),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Floating Neon Particles
            ...List.generate(15, (index) {
              return AnimatedBuilder(
                animation: _particleController,
                builder: (context, child) {
                  final progress = (_particleController.value + index * 0.2) % 1.0;
                  final x = size.width * (0.1 + 0.8 * math.sin(progress * 2 * math.pi + index));
                  final y = size.height * progress;
                  final scale = 0.5 + 0.5 * math.sin(progress * 4 * math.pi);
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Transform.scale(
                      scale: scale,
                      child: Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Color(0xFF00FFFF),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                          gradient: const RadialGradient(
                            colors: [
                              Color(0xFF00FFFF),
                              Color(0xFF0080FF),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Main Content
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    const Spacer(flex: 2),
                    
                    // Neon Logo
                    AnimatedBuilder(
                      animation: _mainController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _mainController.value,
                          child: Opacity(
                            opacity: _mainController.value,
                            child: AnimatedBuilder(
                              animation: _glitchController,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(
                                    _glitchController.value * 2 - 1,
                                    0,
                                  ),
                                  child: Container(
                                    width: 150,
                                    height: 150,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color(0xFF00FFFF),
                                          Color(0xFFFF00FF),
                                          Color(0xFF00FF00),
                                        ],
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Color(0xFF00FFFF),
                                          blurRadius: 30,
                                          spreadRadius: 5,
                                        ),
                                        BoxShadow(
                                          color: Color(0xFFFF00FF),
                                          blurRadius: 50,
                                          spreadRadius: 10,
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.monetization_on_rounded,
                                      size: 80,
                                      color: Colors.black,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 40),
                    
                    // Glitch Text Effect
                    AnimatedBuilder(
                      animation: _mainController,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _mainController.value,
                          child: AnimatedBuilder(
                            animation: _glitchController,
                            builder: (context, child) {
                              return Stack(
                                children: [
                                  // Red glitch layer
                                  Transform.translate(
                                    offset: Offset(-2, 0),
                                    child: Text(
                                      'EASY MONEY',
                                      style: TextStyle(
                                        fontSize: 42,
                                        fontWeight: FontWeight.w900,
                                        color: Colors.red.withOpacity(0.7),
                                        letterSpacing: 3,
                                      ),
                                    ),
                                  ),
                                  // Blue glitch layer
                                  Transform.translate(
                                    offset: Offset(2, 0),
                                    child: Text(
                                      'EASY MONEY',
                                      style: TextStyle(
                                        fontSize: 42,
                                        fontWeight: FontWeight.w900,
                                        color: Colors.blue.withOpacity(0.7),
                                        letterSpacing: 3,
                                      ),
                                    ),
                                  ),
                                  // Main text
                                  ShaderMask(
                                    shaderCallback: (bounds) => const LinearGradient(
                                      colors: [
                                        Color(0xFF00FFFF),
                                        Color(0xFFFF00FF),
                                        Color(0xFF00FF00),
                                      ],
                                    ).createShader(bounds),
                                    child: const Text(
                                      'EASY MONEY',
                                      style: TextStyle(
                                        fontSize: 42,
                                        fontWeight: FontWeight.w900,
                                        color: Colors.white,
                                        letterSpacing: 3,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Subtitle
                    AnimatedBuilder(
                      animation: _mainController,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _mainController.value,
                          child: Shimmer.fromColors(
                            baseColor: const Color(0xFF00FFFF),
                            highlightColor: const Color(0xFFFF00FF),
                            child: const Text(
                              'CYBERPUNK EDITION',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 2,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const Spacer(flex: 2),
                    
                    // Loading Animation
                    AnimatedBuilder(
                      animation: _particleController,
                      builder: (context, child) {
                        return Column(
                          children: [
                            Container(
                              width: 200,
                              height: 4,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2),
                                color: Colors.white.withOpacity(0.1),
                              ),
                              child: Stack(
                                children: [
                                  Container(
                                    width: 200 * _particleController.value,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(2),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color(0xFF00FFFF),
                                          Color(0xFFFF00FF),
                                          Color(0xFF00FF00),
                                        ],
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Color(0xFF00FFFF),
                                          blurRadius: 10,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: 20),
                            
                            Text(
                              'INITIALIZING CYBERPUNK INTERFACE...',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFF00FFFF),
                                fontWeight: FontWeight.w500,
                                letterSpacing: 1,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    
                    const Spacer(flex: 1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
