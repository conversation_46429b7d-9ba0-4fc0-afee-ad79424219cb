import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../widgets/profile_stat_card.dart';
import '../../widgets/profile_menu_item.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Header
                _buildHeader(),
                
                const SizedBox(height: 30),
                
                // Profile Info
                _buildProfileInfo(),
                
                const SizedBox(height: 30),
                
                // Stats
                _buildStats(),
                
                const SizedBox(height: 30),
                
                // Menu Items
                _buildMenuItems(),
                
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Profile',
          style: AppTextStyles.h3.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ).animate().fadeIn(duration: 600.ms),
        
        IconButton(
          onPressed: () {
            // TODO: Implement settings
          },
          icon: const Icon(
            Icons.settings,
            color: AppColors.textSecondary,
          ),
          style: IconButton.styleFrom(
            backgroundColor: AppColors.surface,
            padding: const EdgeInsets.all(12),
          ),
        ).animate().scale(duration: 600.ms, delay: 200.ms),
      ],
    );
  }

  Widget _buildProfileInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Picture
          Stack(
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: AppColors.primaryGradient,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.person,
                  size: 50,
                  color: Colors.white,
                ),
              ),
              
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.earning,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.surface,
                      width: 3,
                    ),
                  ),
                  child: const Icon(
                    Icons.edit,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ).animate().scale(duration: 800.ms, curve: Curves.elasticOut),
          
          const SizedBox(height: 20),
          
          // Name
          Text(
            'John Doe',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 400.ms),
          
          const SizedBox(height: 8),
          
          // Email
          Text(
            '<EMAIL>',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 500.ms),
          
          const SizedBox(height: 16),
          
          // Member since
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Member since Jan 2024',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 600.ms),
        ],
      ),
    ).animate().slideY(duration: 800.ms, delay: 300.ms);
  }

  Widget _buildStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Statistics',
          style: AppTextStyles.h5.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 700.ms),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: ProfileStatCard(
                title: 'Total Earned',
                value: '\$1,247.50',
                icon: Icons.monetization_on,
                color: AppColors.earning,
              ).animate().slideX(duration: 600.ms, delay: 800.ms),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: ProfileStatCard(
                title: 'Videos Watched',
                value: '342',
                icon: Icons.play_circle,
                color: AppColors.primary,
              ).animate().slideX(duration: 600.ms, delay: 900.ms),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: ProfileStatCard(
                title: 'Tasks Completed',
                value: '89',
                icon: Icons.task_alt,
                color: AppColors.accent,
              ).animate().slideX(duration: 600.ms, delay: 1000.ms),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: ProfileStatCard(
                title: 'Games Played',
                value: '156',
                icon: Icons.games,
                color: AppColors.gold,
              ).animate().slideX(duration: 600.ms, delay: 1100.ms),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMenuItems() {
    final menuItems = [
      {
        'icon': Icons.account_balance_wallet,
        'title': 'Wallet',
        'subtitle': 'Manage your earnings',
        'color': AppColors.earning,
      },
      {
        'icon': Icons.history,
        'title': 'Transaction History',
        'subtitle': 'View all transactions',
        'color': AppColors.info,
      },
      {
        'icon': Icons.share,
        'title': 'Refer Friends',
        'subtitle': 'Earn \$10 per referral',
        'color': AppColors.gold,
      },
      {
        'icon': Icons.help_outline,
        'title': 'Help & Support',
        'subtitle': 'Get help when you need it',
        'color': AppColors.primary,
      },
      {
        'icon': Icons.privacy_tip_outlined,
        'title': 'Privacy Policy',
        'subtitle': 'Read our privacy policy',
        'color': AppColors.textSecondary,
      },
      {
        'icon': Icons.logout,
        'title': 'Logout',
        'subtitle': 'Sign out of your account',
        'color': AppColors.error,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account',
          style: AppTextStyles.h5.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 1200.ms),
        
        const SizedBox(height: 16),
        
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: List.generate(
              menuItems.length,
              (index) {
                final item = menuItems[index];
                return ProfileMenuItem(
                  icon: item['icon'] as IconData,
                  title: item['title'] as String,
                  subtitle: item['subtitle'] as String,
                  color: item['color'] as Color,
                  onTap: () => _handleMenuTap(item['title'] as String),
                  showDivider: index < menuItems.length - 1,
                ).animate().slideX(
                  duration: 600.ms,
                  delay: (1300 + index * 100).ms,
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  void _handleMenuTap(String title) {
    switch (title) {
      case 'Wallet':
        // TODO: Navigate to wallet screen
        break;
      case 'Transaction History':
        // TODO: Navigate to transaction history
        break;
      case 'Refer Friends':
        // TODO: Navigate to referral screen
        break;
      case 'Help & Support':
        // TODO: Navigate to help screen
        break;
      case 'Privacy Policy':
        // TODO: Show privacy policy
        break;
      case 'Logout':
        _showLogoutDialog();
        break;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Logout',
          style: AppTextStyles.h5,
        ),
        content: Text(
          'Are you sure you want to logout?',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement logout
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: Text(
              'Logout',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }
}
