import 'package:flutter/material.dart';

class AppDimensions {
  // Modern Spacing System - 8pt Grid
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
  static const double xxxl = 64.0;
  
  // Padding System
  static const EdgeInsets paddingXS = EdgeInsets.all(xs);
  static const EdgeInsets paddingSM = EdgeInsets.all(sm);
  static const EdgeInsets paddingMD = EdgeInsets.all(md);
  static const EdgeInsets paddingLG = EdgeInsets.all(lg);
  static const EdgeInsets paddingXL = EdgeInsets.all(xl);
  static const EdgeInsets paddingXXL = EdgeInsets.all(xxl);

  // Individual Padding Values
  static const double paddingSmall = sm;
  static const double paddingMedium = md;
  static const double paddingLarge = lg;
  
  // Horizontal Padding
  static const EdgeInsets paddingHorizontalXS = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets paddingHorizontalSM = EdgeInsets.symmetric(horizontal: sm);
  static const EdgeInsets paddingHorizontalMD = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets paddingHorizontalLG = EdgeInsets.symmetric(horizontal: lg);
  static const EdgeInsets paddingHorizontalXL = EdgeInsets.symmetric(horizontal: xl);
  
  // Vertical Padding
  static const EdgeInsets paddingVerticalXS = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets paddingVerticalSM = EdgeInsets.symmetric(vertical: sm);
  static const EdgeInsets paddingVerticalMD = EdgeInsets.symmetric(vertical: md);
  static const EdgeInsets paddingVerticalLG = EdgeInsets.symmetric(vertical: lg);
  static const EdgeInsets paddingVerticalXL = EdgeInsets.symmetric(vertical: xl);
  
  // Border Radius System - Modern Rounded Corners
  static const double radiusXS = 4.0;
  static const double radiusSM = 8.0;
  static const double radiusMD = 12.0;
  static const double radiusLG = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusXXL = 32.0;
  static const double radiusCircle = 50.0;
  
  // BorderRadius Objects
  static const BorderRadius borderRadiusXS = BorderRadius.all(Radius.circular(radiusXS));
  static const BorderRadius borderRadiusSM = BorderRadius.all(Radius.circular(radiusSM));
  static const BorderRadius borderRadiusMD = BorderRadius.all(Radius.circular(radiusMD));
  static const BorderRadius borderRadiusLG = BorderRadius.all(Radius.circular(radiusLG));
  static const BorderRadius borderRadiusXL = BorderRadius.all(Radius.circular(radiusXL));
  static const BorderRadius borderRadiusXXL = BorderRadius.all(Radius.circular(radiusXXL));
  static const BorderRadius borderRadiusCircle = BorderRadius.all(Radius.circular(radiusCircle));
  
  // Icon Sizes - Modern Scale
  static const double iconXS = 16.0;
  static const double iconSM = 20.0;
  static const double iconMD = 24.0;
  static const double iconLG = 32.0;
  static const double iconXL = 48.0;
  static const double iconXXL = 64.0;
  static const double iconHero = 96.0;
  
  // Button Heights - Touch Friendly
  static const double buttonHeightSM = 36.0;
  static const double buttonHeightMD = 44.0;
  static const double buttonHeightLG = 52.0;
  static const double buttonHeightXL = 60.0;
  
  // Card Dimensions
  static const double cardElevation = 4.0;
  static const double cardElevationHover = 8.0;
  static const double cardElevationPressed = 2.0;
  
  // Glassmorphism Properties
  static const double glassBlur = 20.0;
  static const double glassOpacity = 0.1;
  static const double glassBorderWidth = 1.0;
  
  // 3D Effect Properties
  static const double shadow3DBlur = 20.0;
  static const double shadow3DSpread = 0.0;
  static const Offset shadow3DOffset = Offset(0, 10);
  
  // Animation Durations
  static const Duration animationFast = Duration(milliseconds: 200);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  static const Duration animationExtraSlow = Duration(milliseconds: 800);
  
  // Screen Breakpoints
  static const double mobileBreakpoint = 480.0;
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;
  
  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 70.0;
  static const double bottomNavIconSize = 28.0;
  
  // Floating Action Button
  static const double fabSize = 56.0;
  static const double fabMiniSize = 40.0;
  
  // List Items
  static const double listItemHeight = 72.0;
  static const double listItemMinHeight = 48.0;
  
  // Divider
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 16.0;
  
  // Progress Indicators
  static const double progressIndicatorSize = 24.0;
  static const double progressIndicatorStroke = 3.0;
  
  // Snackbar
  static const double snackbarElevation = 6.0;
  static const EdgeInsets snackbarMargin = EdgeInsets.all(8.0);
  
  // Dialog
  static const double dialogElevation = 24.0;
  static const EdgeInsets dialogPadding = EdgeInsets.all(24.0);
  
  // Chip
  static const double chipHeight = 32.0;
  static const EdgeInsets chipPadding = EdgeInsets.symmetric(horizontal: 12.0);
  
  // Avatar Sizes
  static const double avatarSizeXS = 24.0;
  static const double avatarSizeSM = 32.0;
  static const double avatarSizeMD = 40.0;
  static const double avatarSizeLG = 56.0;
  static const double avatarSizeXL = 72.0;
  static const double avatarSizeXXL = 96.0;
}
