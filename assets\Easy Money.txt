
Overview
This app allows users to earn real money by completing microtasks like solving short links, playing games, and watching ads. Monetization is shared between users (as earnings) and the app owner (as profit). The system is managed entirely through a powerful admin panel backed by PocketBase as the backend.

Core Features of the App
1. Short Link Tasks
- Each task contains a dynamic number of short links (e.g., 5).
- Admin sets a dynamic goal (e.g., 1000 completions).
- Rewards are given only when the total number of users complete the task.
- Users see a progress bar for each task.
- A custom CAPTCHA screen is shown at the end of the task (inside WebView) for human verification.
- Banner ads shown inside CAPTCHA screen generate extra revenue.
- Rewards are credited first to Partial Wallet and later moved to Real Wallet once the goal is reached.

2. Games with Rewarded Ads
- Includes 5–10 open-source HTML5 games embedded in the app via WebView.
- Rewarded ads shown after each game win.
- Daily missions: “Win 5 games – ₹5”, “Win 10 games – ₹10”.
- Missions refresh daily to ensure continued user engagement and monetization.

3. Watch Ads
- Users can watch up to 25 rewarded ads per day.
- Each view adds revenue to the app and a share to the user's Partial Wallet.
- Helps increase daily active users and session time.

4. Task Completion CAPTCHA
- Each short link task ends with a CAPTCHA screen hosted in a WebView.
- Clicking “Verify” confirms task completion and shows a banner ad.
- The CAPTCHA page ensures bot protection and boosts banner ad impressions.
- Can be customized to require user interaction (e.g., checkbox or drag-to-verify).

Wallet System
1. Partial Wallet
- Temporary wallet to hold pending earnings.
- Used when a user completes a task that hasn't yet reached its goal.
- Not eligible for withdrawal.
- Visible to user for tracking potential income.

2. Real Wallet
- Earnings moved here only after the task goal is achieved.
- Eligible for withdrawal.
- Withdrawal requests must be approved manually by the admin.
- Actual payments are made only from the Real Wallet.

Wallet Comparison Table:
Partial Wallet vs Real Wallet:
- Withdrawal Allowed? No | Yes
- Purpose: Hold task earnings temporarily | Hold confirmed, withdrawable funds
- Admin Approval? No | Yes
- Visibility: Yes | Yes

Admin Panel Features
User Management
- Track all registered users and real-time online users.
- Ban/unban users and devices.
- Monitor user task activity and history.

Task Management
- Add/delete/edit tasks with dynamic:
  - Number of short links
  - Completion goals
  - Reward per task
- Monitor how many users completed each task.
- View real-time link clicks, task progress, and conversions.

Wallet & Withdrawal Management
- Track user wallet balances.
- View and approve withdrawal requests.
- Audit all transactions with exact timestamps and sources.

Ad & Revenue Analytics
- View total ads played, tasks completed, game missions finished, links clicked.
- Daily, weekly, monthly revenue summaries.
- Breakdown by short links, banner ads, rewarded ads, and games.

Fraud & Security Tools
- Device fingerprinting.
- Task completion speed monitoring.
- CAPTCHA validation logs.

Admin Tools
- Global messaging.
- Push notification system.
- Export data reports.
- Real-time dashboard view.

Backend
- Powered by PocketBase for authentication, database, real-time syncing, file storage.

Monetization Strategy Table:
Source         | Income Method              | User Share | Admin Share
Short Links    | CPM (e.g., ₹6–₹20 per 1000) | ~80%       | ~20%
Rewarded Ads   | Paid per view               | Yes        | Yes
Games          | Ads triggered by gameplay   | Yes        | Yes
CAPTCHA Ads    | Banner ads in CAPTCHA page  | No         | 100%

Final Notes
- Fully dynamic control for tasks and rewards.
- All earnings traceable.
- Low risk as payouts depend on real CPM earnings.
