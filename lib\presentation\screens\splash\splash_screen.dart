import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import '../../../core/constants/app_colors.dart';
import '../home/<USER>';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late AnimationController _particleController;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _startAnimationSequence();
  }

  void _startAnimationSequence() async {
    // Start particle animation immediately
    _particleController.repeat();
    
    // Delay then start main animations
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Start animations in sequence
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _slideController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _pulseController.repeat();
    
    // Navigate after animations complete
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const HomeScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeInOut),
              ),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Floating Particles - Inspired by Augment website
            ...List.generate(20, (index) {
              return AnimatedBuilder(
                animation: _particleController,
                builder: (context, child) {
                  final progress = (_particleController.value + index * 0.1) % 1.0;
                  final x = size.width * (0.1 + 0.8 * math.sin(index * 0.5));
                  final y = size.height * (1.2 - progress * 1.4);
                  final opacity = (1 - progress) * 0.4;
                  final scale = 0.5 + 0.5 * math.sin(progress * 2 * math.pi);
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Transform.scale(
                      scale: scale,
                      child: Container(
                        width: 4 + (index % 3) * 2,
                        height: 4 + (index % 3) * 2,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              AppColors.primary.withOpacity(opacity),
                              AppColors.primaryLight.withOpacity(opacity * 0.5),
                              Colors.transparent,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withOpacity(opacity * 0.5),
                              blurRadius: 8,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Main Content
            SafeArea(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 24 : 40,
                  vertical: isSmallScreen ? 40 : 60,
                ),
                child: Column(
                  children: [
                    const Spacer(flex: 2),
                    
                    // Logo Section with smooth animations
                    AnimatedBuilder(
                      animation: _fadeController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeController,
                          child: AnimatedBuilder(
                            animation: _slideController,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(
                                  0,
                                  50 * (1 - _slideController.value),
                                ),
                                child: Column(
                                  children: [
                                    // Elegant Logo
                                    AnimatedBuilder(
                                      animation: _pulseController,
                                      builder: (context, child) {
                                        return Transform.scale(
                                          scale: 1 + (_pulseController.value * 0.05),
                                          child: Container(
                                            width: isSmallScreen ? 120 : 140,
                                            height: isSmallScreen ? 120 : 140,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              gradient: LinearGradient(
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                                colors: AppColors.primaryGradient,
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: AppColors.primary.withOpacity(0.4),
                                                  blurRadius: 30,
                                                  spreadRadius: 5,
                                                ),
                                                BoxShadow(
                                                  color: AppColors.primary.withOpacity(0.2),
                                                  blurRadius: 60,
                                                  spreadRadius: 10,
                                                ),
                                              ],
                                            ),
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                // Inner glow
                                                Container(
                                                  width: 80,
                                                  height: 80,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    gradient: RadialGradient(
                                                      colors: [
                                                        Colors.white.withOpacity(0.3),
                                                        Colors.transparent,
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                // Main icon
                                                Icon(
                                                  Icons.monetization_on_rounded,
                                                  size: isSmallScreen ? 60 : 70,
                                                  color: AppColors.textOnPrimary,
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    
                                    SizedBox(height: isSmallScreen ? 40 : 50),
                                    
                                    // App Name with smooth typewriter effect
                                    ShaderMask(
                                      shaderCallback: (bounds) => LinearGradient(
                                        colors: [
                                          AppColors.primary,
                                          AppColors.primaryLight,
                                          AppColors.primary,
                                        ],
                                      ).createShader(bounds),
                                      child: AnimatedTextKit(
                                        animatedTexts: [
                                          TypewriterAnimatedText(
                                            'Easy Money',
                                            textStyle: TextStyle(
                                              fontSize: isSmallScreen ? 42 : 52,
                                              fontWeight: FontWeight.w900,
                                              color: Colors.white,
                                              letterSpacing: 1,
                                            ),
                                            speed: const Duration(milliseconds: 100),
                                          ),
                                        ],
                                        totalRepeatCount: 1,
                                      ),
                                    ),
                                    
                                    SizedBox(height: isSmallScreen ? 15 : 20),
                                    
                                    // Subtitle with fade-in effect
                                    Text(
                                      'Your Path to Financial Freedom',
                                      style: TextStyle(
                                        fontSize: isSmallScreen ? 16 : 18,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.textSecondary,
                                        letterSpacing: 0.5,
                                      ),
                                      textAlign: TextAlign.center,
                                    ).animate()
                                      .fadeIn(delay: 2000.ms, duration: 800.ms)
                                      .slideY(begin: 0.3, delay: 2000.ms, duration: 800.ms),
                                  ],
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                    
                    const Spacer(flex: 2),
                    
                    // Loading Section with smooth progress
                    AnimatedBuilder(
                      animation: _particleController,
                      builder: (context, child) {
                        return Column(
                          children: [
                            // Smooth progress bar
                            Container(
                              width: isSmallScreen ? 200 : 250,
                              height: 4,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2),
                                color: AppColors.surface,
                              ),
                              child: Stack(
                                children: [
                                  Container(
                                    width: (isSmallScreen ? 200 : 250) * _particleController.value,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(2),
                                      gradient: LinearGradient(
                                        colors: AppColors.primaryGradient,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primary.withOpacity(0.5),
                                          blurRadius: 8,
                                          spreadRadius: 1,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            
                            SizedBox(height: isSmallScreen ? 25 : 30),
                            
                            // Loading text
                            Text(
                              'Loading your financial journey...',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 14 : 16,
                                color: AppColors.textSecondary,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    
                    const Spacer(flex: 1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
