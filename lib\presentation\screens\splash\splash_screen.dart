import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:lottie/lottie.dart';
import 'package:glassmorphism/glassmorphism.dart';
import 'package:simple_animations/simple_animations.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../../core/constants/app_dimensions.dart';
import '../onboarding/onboarding_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particleController;
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _3dController;
  late AnimationController _morphController;

  // 3D Cube Controller
  late Object cube;
  late Scene scene;

  // Particle System
  List<Particle> particles = [];

  // Glassmorphism effects
  bool _showGlassEffect = false;

  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    // Main animation controller
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 3500),
      vsync: this,
    );

    // Particle animation controller
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    // Rotation animation controller
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 4000),
      vsync: this,
    )..repeat();

    // Pulse animation controller
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    // 3D animation controller
    _3dController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    // Morphing animation controller
    _morphController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    // Initialize 3D scene and particles
    _init3DScene();
    _initParticles();

    // Show glass effect after delay
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showGlassEffect = true;
        });
      }
    });


    // Define animations
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_rotationController);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeInOut),
    ));

    _slideAnimation = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.3, 0.9, curve: Curves.easeOutBack),
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _init3DScene() {
    // Initialize 3D scene with custom implementation
    // Using transform matrices for 3D effects instead of flutter_cube
    // This provides better performance and compatibility
  }

  void _initParticles() {
    // Initialize particle system
    particles = List.generate(100, (index) {
      return Particle(
        position: Offset(
          math.Random().nextDouble() * 400,
          math.Random().nextDouble() * 800,
        ),
        velocity: Offset(
          (math.Random().nextDouble() - 0.5) * 2,
          (math.Random().nextDouble() - 0.5) * 2,
        ),
        size: math.Random().nextDouble() * 3 + 1,
        color: Colors.white.withOpacity(math.Random().nextDouble() * 0.5),
      );
    });
  }

  void _startSplashSequence() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _mainController.forward();

    // Navigate to onboarding
    await Future.delayed(const Duration(milliseconds: 3500));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const OnboardingScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1.0, 0.0),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeInOutCubic,
                )),
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 1000),
        ),
      );
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particleController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _3dController.dispose();
    _morphController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.backgroundDark,
              AppColors.backgroundDarkGlass,
              AppColors.surfaceDark,
              AppColors.backgroundDark,
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // Animated Particle Background
            ...List.generate(50, (index) {
              return AnimatedBuilder(
                animation: _particleController,
                builder: (context, child) {
                  final progress = (_particleController.value + index * 0.1) % 1.0;
                  final x = size.width * (0.1 + 0.8 * math.sin(progress * 2 * math.pi + index));
                  final y = size.height * progress;
                  final opacity = (1.0 - progress) * 0.6;

                  return Positioned(
                    left: x,
                    top: y,
                    child: Container(
                      width: 4 + (index % 3) * 2,
                      height: 4 + (index % 3) * 2,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            AppColors.primary.withOpacity(opacity),
                            AppColors.secondary.withOpacity(opacity * 0.5),
                            Colors.transparent,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(opacity * 0.5),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            }),

            // Glassmorphism Overlay with Blur
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.glassWhite.withOpacity(0.1),
                      AppColors.glassDark.withOpacity(0.05),
                    ],
                  ),
                ),
              ),
            ),



            // Main Content with Stunning Design
            SafeArea(
              child: SingleChildScrollView(
                child: SizedBox(
                  height: size.height - MediaQuery.of(context).padding.top - MediaQuery.of(context).padding.bottom,
                  child: Column(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                          // Stunning 3D Logo with Neon Effects
                          AnimatedBuilder(
                            animation: Listenable.merge([
                              _mainController,
                              _rotationController,
                              _pulseController,
                            ]),
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _scaleAnimation.value * _pulseAnimation.value,
                                child: Transform(
                                  alignment: Alignment.center,
                                  transform: Matrix4.identity()
                                    ..setEntry(3, 2, 0.001)
                                    ..rotateX(_rotationAnimation.value * 0.2)
                                    ..rotateY(_rotationAnimation.value * 0.3)
                                    ..rotateZ(_rotationAnimation.value * 0.05),
                                  child: Container(
                                    width: size.width * 0.35,
                                    height: size.width * 0.35,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(40),
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: AppColors.primaryGradient,
                                      ),
                                      border: Border.all(
                                        color: AppColors.glassWhite,
                                        width: 2,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primary.withOpacity(0.5),
                                          blurRadius: 40,
                                          offset: const Offset(0, 20),
                                          spreadRadius: 0,
                                        ),
                                        BoxShadow(
                                          color: AppColors.secondary.withOpacity(0.3),
                                          blurRadius: 60,
                                          offset: const Offset(0, 30),
                                          spreadRadius: 10,
                                        ),
                                        BoxShadow(
                                          color: AppColors.accent.withOpacity(0.2),
                                          blurRadius: 80,
                                          offset: const Offset(0, 40),
                                          spreadRadius: 20,
                                        ),
                                      ],
                                  ),
                                    child: Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        // Outer Glow Ring
                                        Container(
                                          width: 120,
                                          height: 120,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: RadialGradient(
                                              colors: [
                                                Colors.transparent,
                                                AppColors.primary.withOpacity(0.1),
                                                AppColors.secondary.withOpacity(0.2),
                                                Colors.transparent,
                                              ],
                                              stops: const [0.0, 0.5, 0.8, 1.0],
                                            ),
                                          ),
                                        ),
                                        // Inner Glow
                                        Container(
                                          width: 90,
                                          height: 90,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: RadialGradient(
                                              colors: [
                                                AppColors.primary.withOpacity(0.4),
                                                AppColors.secondary.withOpacity(0.2),
                                                Colors.transparent,
                                              ],
                                            ),
                                          ),
                                        ),
                                        // Animated Money Icon with Shimmer
                                        Shimmer.fromColors(
                                          baseColor: Colors.white,
                                          highlightColor: AppColors.gold,
                                          period: const Duration(milliseconds: 1500),
                                          child: Icon(
                                            Icons.monetization_on_rounded,
                                            size: 80,
                                            color: Colors.white,
                                          ),
                                        ),
                                        // Floating Dollar Signs
                                        ...List.generate(3, (index) {
                                          return AnimatedBuilder(
                                            animation: _rotationController,
                                            builder: (context, child) {
                                              final angle = _rotationController.value * 2 * math.pi + (index * 2 * math.pi / 3);
                                              final radius = 50.0;
                                              final x = radius * math.cos(angle);
                                              final y = radius * math.sin(angle);

                                              return Transform.translate(
                                                offset: Offset(x, y),
                                                child: Transform.scale(
                                                  scale: 0.5 + 0.3 * math.sin(_rotationController.value * 4 * math.pi + index),
                                                  child: Icon(
                                                    Icons.attach_money,
                                                    size: 20,
                                                    color: AppColors.gold.withOpacity(0.8),
                                                  ),
                                                ),
                                              );
                                            },
                                          );
                                        }),
                                      ],
                                    ),
                                ),
                              ),
                            );
                          },
                        ),

                          SizedBox(height: size.height * 0.03),

                          // Stunning App Name with Glassmorphism
                          AnimatedBuilder(
                            animation: _fadeAnimation,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(0, _slideAnimation.value),
                                child: Opacity(
                                  opacity: _fadeAnimation.value,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 30,
                                      vertical: 20,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(25),
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          AppColors.glassWhite.withOpacity(0.2),
                                          AppColors.glassWhite.withOpacity(0.1),
                                        ],
                                      ),
                                      border: Border.all(
                                        color: AppColors.glassBorder,
                                        width: 1.5,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primary.withOpacity(0.2),
                                          blurRadius: 20,
                                          offset: const Offset(0, 10),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      children: [
                                        // Main App Name with Gradient Text
                                        ShaderMask(
                                          shaderCallback: (bounds) => LinearGradient(
                                            colors: [
                                              AppColors.primary,
                                              AppColors.secondary,
                                              AppColors.accent,
                                            ],
                                          ).createShader(bounds),
                                          child: AnimatedTextKit(
                                            animatedTexts: [
                                              TypewriterAnimatedText(
                                                'Easy Money',
                                                textStyle: TextStyle(
                                                  fontSize: size.width * 0.12,
                                                  fontWeight: FontWeight.w900,
                                                  color: Colors.white,
                                                  letterSpacing: 3,
                                                  shadows: [
                                                    Shadow(
                                                      offset: Offset(0, 0),
                                                      blurRadius: 20,
                                                      color: AppColors.primary,
                                                    ),
                                                    Shadow(
                                                      offset: Offset(2, 2),
                                                      blurRadius: 4,
                                                      color: Colors.black54,
                                                    ),
                                                  ],
                                                ),
                                                speed: const Duration(milliseconds: 100),
                                              ),
                                            ],
                                            totalRepeatCount: 1,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        // Subtitle with Shimmer Effect
                                        Shimmer.fromColors(
                                          baseColor: AppColors.textSecondaryDark,
                                          highlightColor: AppColors.primary,
                                          period: const Duration(milliseconds: 2000),
                                          child: Text(
                                            'Premium Earning Experience',
                                            style: TextStyle(
                                              fontSize: size.width * 0.04,
                                              fontWeight: FontWeight.w500,
                                              letterSpacing: 1.5,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 15),

                        // Animated Tagline
                        AnimatedBuilder(
                          animation: _fadeAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(0, _slideAnimation.value * 0.5),
                              child: Opacity(
                                opacity: _fadeAnimation.value * 0.9,
                                child: Text(
                                  'Earn Money Easily & Smartly',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w300,
                                    letterSpacing: 1,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(1, 1),
                                        blurRadius: 2,
                                        color: Colors.black26,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                  // Stunning Loading Section
                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Modern Progress Ring
                        AnimatedBuilder(
                          animation: _particleController,
                          builder: (context, child) {
                            return Stack(
                              alignment: Alignment.center,
                              children: [
                                // Outer Ring
                                SizedBox(
                                  width: 80,
                                  height: 80,
                                  child: CircularProgressIndicator(
                                    value: _particleController.value,
                                    strokeWidth: 6,
                                    backgroundColor: AppColors.surfaceVariantDark,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.primary,
                                    ),
                                  ),
                                ),
                                // Inner Ring
                                SizedBox(
                                  width: 60,
                                  height: 60,
                                  child: CircularProgressIndicator(
                                    value: _particleController.value * 0.7,
                                    strokeWidth: 4,
                                    backgroundColor: Colors.transparent,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.secondary,
                                    ),
                                  ),
                                ),
                                // Center Glow
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: RadialGradient(
                                      colors: [
                                        AppColors.primary.withOpacity(0.6),
                                        AppColors.secondary.withOpacity(0.3),
                                        Colors.transparent,
                                      ],
                                    ),
                                  ),
                                ),
                                // Progress Text
                                Text(
                                  '${(_particleController.value * 100).toInt()}%',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),

                        SizedBox(height: size.height * 0.02),

                        // Dynamic Loading Text
                        AnimatedBuilder(
                          animation: _fadeAnimation,
                          builder: (context, child) {
                            final loadingTexts = [
                              'Initializing Premium Experience...',
                              'Loading Stunning Interface...',
                              'Preparing Your Journey...',
                              'Almost Ready...',
                            ];
                            final textIndex = (_particleController.value * loadingTexts.length).floor();
                            final currentText = loadingTexts[textIndex.clamp(0, loadingTexts.length - 1)];

                            return Opacity(
                              opacity: _fadeAnimation.value * 0.9,
                              child: Text(
                                currentText,
                                style: TextStyle(
                                  fontSize: 18,
                                  color: AppColors.textSecondaryDark,
                                  fontWeight: FontWeight.w400,
                                  letterSpacing: 1.2,
                                  shadows: [
                                    Shadow(
                                      offset: const Offset(0, 0),
                                      blurRadius: 10,
                                      color: AppColors.primary.withOpacity(0.3),
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),

                  // Stunning Bottom Floating Elements
                  Padding(
                    padding: const EdgeInsets.only(bottom: 60),
                    child: Column(
                      children: [
                        // Premium Floating Icons with Neon Effects
                        SizedBox(
                          height: 100,
                          child: Stack(
                            children: List.generate(6, (index) {
                              final positions = [
                                const Offset(0.1, 0.2),
                                const Offset(0.85, 0.1),
                                const Offset(0.25, 0.7),
                                const Offset(0.75, 0.8),
                                const Offset(0.5, 0.3),
                                const Offset(0.15, 0.9),
                              ];
                              final icons = [
                                Icons.attach_money_rounded,
                                Icons.monetization_on_rounded,
                                Icons.diamond_rounded,
                                Icons.star_rounded,
                                Icons.auto_awesome_rounded,
                                Icons.currency_bitcoin_rounded,
                              ];
                              final colors = [
                                AppColors.gold,
                                AppColors.primary,
                                AppColors.accent,
                                AppColors.secondary,
                                AppColors.accentSecondary,
                                AppColors.accentTertiary,
                              ];

                              return Positioned(
                                left: size.width * positions[index].dx,
                                top: 100 * positions[index].dy,
                                child: AnimatedBuilder(
                                  animation: _rotationController,
                                  builder: (context, child) {
                                    final scale = 0.6 + 0.5 * math.sin(
                                      _rotationController.value * 3 * math.pi + index * 0.5,
                                    );
                                    final rotation = _rotationController.value * 2 * math.pi + index;

                                    return Transform.scale(
                                      scale: scale,
                                      child: Transform.rotate(
                                        angle: rotation,
                                        child: Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: RadialGradient(
                                              colors: [
                                                colors[index].withOpacity(0.3),
                                                colors[index].withOpacity(0.1),
                                                Colors.transparent,
                                              ],
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: colors[index].withOpacity(0.4),
                                                blurRadius: 15,
                                                spreadRadius: 2,
                                              ),
                                            ],
                                          ),
                                          child: Icon(
                                            icons[index],
                                            color: colors[index],
                                            size: 20 + index * 2,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              );
                            }),
                          ),
                        ),

                        SizedBox(height: size.height * 0.02),

                        // Premium Version Text with Glow
                        AnimatedBuilder(
                          animation: _fadeAnimation,
                          builder: (context, child) {
                            return Opacity(
                              opacity: _fadeAnimation.value * 0.7,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.glassWhite.withOpacity(0.1),
                                      AppColors.glassWhite.withOpacity(0.05),
                                    ],
                                  ),
                                  border: Border.all(
                                    color: AppColors.glassBorder.withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  'Premium Edition v2.0.0',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.textSecondaryDark,
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: 1,
                                    shadows: [
                                      Shadow(
                                        offset: const Offset(0, 0),
                                        blurRadius: 8,
                                        color: AppColors.primary.withOpacity(0.3),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          ],
        ),
      ),
    );
  }
}

// Custom Painter for Particle Effects
class ParticlesPainter extends CustomPainter {
  final double animationValue;

  ParticlesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 50; i++) {
      final x = (size.width * (i * 0.1 + animationValue * 0.5)) % size.width;
      final y = (size.height * (i * 0.07 + animationValue * 0.3)) % size.height;
      final radius = 1.0 + (i % 3).toDouble();

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Particle class for advanced particle system
class Particle {
  Offset position;
  Offset velocity;
  double size;
  Color color;

  Particle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.color,
  });

  void update() {
    position += velocity;
  }
}
