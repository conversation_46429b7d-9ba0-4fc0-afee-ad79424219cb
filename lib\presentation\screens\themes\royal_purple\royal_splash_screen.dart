import 'package:flutter/material.dart';

class RoyalSplashScreen extends StatelessWidget {
  const RoyalSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6A0DAD),
              Color(0xFF9370DB),
              Color(0xFFFFD700),
            ],
          ),
        ),
        child: const Center(
          child: Text(
            'Royal Purple Theme',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
