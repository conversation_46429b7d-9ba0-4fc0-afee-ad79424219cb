import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../widgets/video_card.dart';

class VideosScreen extends StatefulWidget {
  const VideosScreen({super.key});

  @override
  State<VideosScreen> createState() => _VideosScreenState();
}

class _VideosScreenState extends State<VideosScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedCategory = 0;

  final List<String> _categories = [
    'All',
    'Tech',
    'Entertainment',
    'Education',
    'Gaming',
    'Lifestyle',
  ];

  final List<VideoData> _videos = [
    VideoData(
      id: '1',
      title: 'iPhone 15 Pro Max Review - Is it Worth the Upgrade?',
      thumbnail: 'https://picsum.photos/400/225?random=1',
      duration: '12:45',
      reward: 0.75,
      category: 'Tech',
      views: '2.1M',
      isWatched: false,
    ),
    VideoData(
      id: '2',
      title: 'Top 10 Mobile Apps You Must Try in 2024',
      thumbnail: 'https://picsum.photos/400/225?random=2',
      duration: '8:30',
      reward: 0.50,
      category: 'Tech',
      views: '1.5M',
      isWatched: true,
    ),
    VideoData(
      id: '3',
      title: 'Funny Cat Compilation - Laugh Out Loud!',
      thumbnail: 'https://picsum.photos/400/225?random=3',
      duration: '5:20',
      reward: 0.25,
      category: 'Entertainment',
      views: '3.2M',
      isWatched: false,
    ),
    VideoData(
      id: '4',
      title: 'Learn Flutter in 30 Minutes - Complete Tutorial',
      thumbnail: 'https://picsum.photos/400/225?random=4',
      duration: '30:15',
      reward: 1.50,
      category: 'Education',
      views: '890K',
      isWatched: false,
    ),
    VideoData(
      id: '5',
      title: 'Best Gaming Setup Under \$1000',
      thumbnail: 'https://picsum.photos/400/225?random=5',
      duration: '15:40',
      reward: 1.00,
      category: 'Gaming',
      views: '1.8M',
      isWatched: false,
    ),
    VideoData(
      id: '6',
      title: '10 Life Hacks That Will Change Your Day',
      thumbnail: 'https://picsum.photos/400/225?random=6',
      duration: '7:25',
      reward: 0.40,
      category: 'Lifestyle',
      views: '2.5M',
      isWatched: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // Categories
              _buildCategories(),
              
              // Videos List
              Expanded(
                child: _buildVideosList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Watch & Earn',
                style: AppTextStyles.h3.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 600.ms),
              
              const SizedBox(height: 4),
              
              Text(
                'Earn coins by watching videos',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
            ],
          ),
          
          // Today's earnings
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.earningGradient,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.earning.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.monetization_on,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '\$12.50',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ).animate().scale(duration: 600.ms, delay: 400.ms),
        ],
      ),
    );
  }

  Widget _buildCategories() {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(bottom: 20),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final isSelected = _selectedCategory == index;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = index;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary : AppColors.surface,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected
                      ? AppColors.primary
                      : AppColors.border.withOpacity(0.5),
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : [],
              ),
              child: Text(
                _categories[index],
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isSelected ? Colors.white : AppColors.textSecondary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ).animate().scale(duration: 200.ms),
          );
        },
      ),
    );
  }

  Widget _buildVideosList() {
    final filteredVideos = _selectedCategory == 0
        ? _videos
        : _videos
            .where((video) => video.category == _categories[_selectedCategory])
            .toList();

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: filteredVideos.length,
      itemBuilder: (context, index) {
        final video = filteredVideos[index];
        return VideoCard(
          video: video,
          onTap: () => _watchVideo(video),
        ).animate().slideX(
          duration: 600.ms,
          delay: (index * 100).ms,
        );
      },
    );
  }

  void _watchVideo(VideoData video) {
    // TODO: Implement video watching functionality
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Watch Video',
          style: AppTextStyles.h5,
        ),
        content: Text(
          'You will earn \$${video.reward.toStringAsFixed(2)} for watching "${video.title}"',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Simulate watching video
              setState(() {
                video.isWatched = true;
              });
              _showEarningDialog(video.reward);
            },
            child: Text(
              'Watch Now',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _showEarningDialog(double amount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppColors.earningGradient,
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 40,
              ),
            ).animate().scale(duration: 600.ms, curve: Curves.elasticOut),
            
            const SizedBox(height: 20),
            
            Text(
              'Congratulations!',
              style: AppTextStyles.h5.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'You earned \$${amount.toStringAsFixed(2)}',
              style: AppTextStyles.h4.copyWith(
                color: AppColors.earning,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Continue',
                style: AppTextStyles.buttonMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class VideoData {
  final String id;
  final String title;
  final String thumbnail;
  final String duration;
  final double reward;
  final String category;
  final String views;
  bool isWatched;

  VideoData({
    required this.id,
    required this.title,
    required this.thumbnail,
    required this.duration,
    required this.reward,
    required this.category,
    required this.views,
    required this.isWatched,
  });
}
