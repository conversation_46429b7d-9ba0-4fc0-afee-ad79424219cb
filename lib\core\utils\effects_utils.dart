import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';

class EffectsUtils {
  // 3D Floating Animation
  static Widget floating3D({
    required Widget child,
    Duration duration = const Duration(seconds: 3),
    double offset = 10.0,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, math.sin(value * 2 * math.pi) * offset),
          child: child,
        );
      },
      child: child,
    ).animate(onPlay: (controller) => controller.repeat());
  }

  // Pulsing Glow Effect
  static Widget pulsingGlow({
    required Widget child,
    Color glowColor = AppColors.primary,
    double glowRadius = 20.0,
    Duration duration = const Duration(seconds: 2),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: glowColor.withOpacity(0.6 * value),
                blurRadius: glowRadius * (0.5 + 0.5 * value),
                spreadRadius: 5.0 * value,
              ),
            ],
          ),
          child: child,
        );
      },
      child: child,
    ).animate(onPlay: (controller) => controller.repeat(reverse: true));
  }

  // 3D Card with Depth
  static Widget card3D({
    required Widget child,
    double depth = 10.0,
    Color shadowColor = AppColors.shadow3D,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin,
        child: Stack(
          children: [
            // Shadow layers for depth
            for (int i = 0; i < depth.toInt(); i++)
              Positioned(
                left: i * 0.5,
                top: i * 0.5,
                child: Container(
                  padding: padding ?? AppDimensions.paddingMD,
                  decoration: BoxDecoration(
                    color: shadowColor.withOpacity(0.1 - (i * 0.01)),
                    borderRadius: borderRadius ?? AppDimensions.borderRadiusLG,
                  ),
                  child: Opacity(opacity: 0, child: child),
                ),
              ),
            // Main card
            Container(
              padding: padding ?? AppDimensions.paddingMD,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: borderRadius ?? AppDimensions.borderRadiusLG,
                boxShadow: [
                  BoxShadow(
                    color: shadowColor,
                    blurRadius: depth,
                    offset: Offset(depth * 0.3, depth * 0.5),
                  ),
                ],
              ),
              child: child,
            ),
          ],
        ),
      ),
    );
  }

  // Morphing Container
  static Widget morphingContainer({
    required Widget child,
    required List<Color> colors,
    Duration duration = const Duration(seconds: 4),
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        final colorIndex = (value * (colors.length - 1)).floor();
        final nextColorIndex = (colorIndex + 1) % colors.length;
        final t = (value * (colors.length - 1)) - colorIndex;
        
        return Container(
          padding: padding ?? AppDimensions.paddingMD,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.lerp(colors[colorIndex], colors[nextColorIndex], t)!,
                Color.lerp(colors[colorIndex], colors[nextColorIndex], t)!.withOpacity(0.7),
              ],
            ),
            borderRadius: borderRadius ?? AppDimensions.borderRadiusLG,
          ),
          child: child,
        );
      },
      child: child,
    ).animate(onPlay: (controller) => controller.repeat());
  }

  // Particle Burst Effect
  static Widget particleBurst({
    required Widget child,
    int particleCount = 20,
    Color particleColor = AppColors.particleGold,
    double maxRadius = 100.0,
    Duration duration = const Duration(seconds: 2),
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        child,
        ...List.generate(particleCount, (index) {
          final angle = (index / particleCount) * 2 * math.pi;
          return TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: duration,
            builder: (context, value, child) {
              return Positioned(
                left: math.cos(angle) * maxRadius * value,
                top: math.sin(angle) * maxRadius * value,
                child: Opacity(
                  opacity: 1.0 - value,
                  child: Container(
                    width: 4.0,
                    height: 4.0,
                    decoration: BoxDecoration(
                      color: particleColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              );
            },
          );
        }),
      ],
    );
  }

  // Shimmer Loading Effect
  static Widget shimmerLoading({
    required Widget child,
    Color baseColor = AppColors.shimmerBase,
    Color highlightColor = AppColors.shimmerHighlight,
    Duration duration = const Duration(milliseconds: 1500),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: -1.0, end: 2.0),
      duration: duration,
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                math.max(0.0, value - 0.3),
                value,
                math.min(1.0, value + 0.3),
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: child,
    ).animate(onPlay: (controller) => controller.repeat());
  }

  // Neon Glow Text
  static Widget neonText({
    required String text,
    required TextStyle style,
    Color glowColor = AppColors.primary,
    double glowRadius = 10.0,
  }) {
    return Stack(
      children: [
        // Glow layers
        for (int i = 1; i <= 3; i++)
          Text(
            text,
            style: style.copyWith(
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = i * 2.0
                ..color = glowColor.withOpacity(0.3 / i),
            ),
          ),
        // Main text
        Text(
          text,
          style: style.copyWith(color: Colors.white),
        ),
      ],
    );
  }

  // Ripple Effect
  static Widget rippleEffect({
    required Widget child,
    Color rippleColor = AppColors.primary,
    Duration duration = const Duration(seconds: 2),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        return CustomPaint(
          painter: RipplePainter(
            animationValue: value,
            color: rippleColor,
          ),
          child: child,
        );
      },
      child: child,
    ).animate(onPlay: (controller) => controller.repeat());
  }

  // Holographic Effect
  static Widget holographic({
    required Widget child,
    List<Color>? colors,
    Duration duration = const Duration(seconds: 3),
  }) {
    final holographicColors = colors ?? [
      AppColors.primary,
      AppColors.accent,
      AppColors.accentSecondary,
      AppColors.accentTertiary,
    ];

    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: duration,
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: holographicColors,
              stops: [
                (value - 0.3).clamp(0.0, 1.0),
                (value - 0.1).clamp(0.0, 1.0),
                (value + 0.1).clamp(0.0, 1.0),
                (value + 0.3).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: child,
    ).animate(onPlay: (controller) => controller.repeat());
  }
}

// Custom Painter for Ripple Effect
class RipplePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  RipplePainter({
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = math.max(size.width, size.height) / 2;
    
    for (int i = 0; i < 3; i++) {
      final radius = maxRadius * animationValue * (1 - i * 0.3);
      final opacity = (1 - animationValue) * (1 - i * 0.3);
      
      if (radius > 0 && opacity > 0) {
        final paint = Paint()
          ..color = color.withOpacity(opacity * 0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;
        
        canvas.drawCircle(center, radius, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
