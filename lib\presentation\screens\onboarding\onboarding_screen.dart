import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lottie/lottie.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/utils/glassmorphism_utils.dart';
import '../../../core/utils/effects_utils.dart';
import '../auth/login_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  late AnimationController _backgroundController;
  late AnimationController _floatingController;
  late AnimationController _particleController;

  late Animation<double> _backgroundAnimation;
  late Animation<double> _floatingAnimation;

  final List<OnboardingData> _onboardingData = [
    OnboardingData(
      icon: Icons.play_circle_filled,
      title: 'Watch Videos & Earn',
      description:
          'Watch engaging videos and earn coins for every video you complete. Experience premium content while earning!',
      color: AppColors.primary,
      gradient: [AppColors.primary, AppColors.secondary],
      lottieAsset: null, // We'll use icons for now since we don't have Lottie files
    ),
    OnboardingData(
      icon: Icons.task_alt,
      title: 'Complete Tasks',
      description:
          'Complete daily tasks, surveys, and challenges to earn more rewards and boost your earnings exponentially.',
      color: AppColors.accent,
      gradient: [AppColors.accent, AppColors.primary],
      lottieAsset: null,
    ),
    OnboardingData(
      icon: Icons.games,
      title: 'Play Games',
      description:
          'Play fun games and earn coins while having a great time. Gaming has never been this rewarding and exciting!',
      color: AppColors.earning,
      gradient: [AppColors.earning, AppColors.accent],
      lottieAsset: null,
    ),
    OnboardingData(
      icon: Icons.account_balance_wallet,
      title: 'Withdraw Earnings',
      description:
          'Easily withdraw your earnings to your preferred payment method. Fast, secure, and reliable transactions.',
      color: AppColors.gold,
      gradient: [AppColors.gold, AppColors.earning],
      lottieAsset: null,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _particleController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..repeat();

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _floatingAnimation = Tween<double>(
      begin: -10.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _floatingController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Stack(
        children: [
          // Animated Background
          AnimatedBuilder(
            animation: _backgroundAnimation,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _onboardingData[_currentPage].gradient[0].withOpacity(0.8),
                      _onboardingData[_currentPage].gradient[1].withOpacity(0.6),
                      AppColors.primary.withOpacity(0.4),
                    ],
                    stops: [
                      0.0,
                      0.5 + 0.3 * math.sin(_backgroundAnimation.value * 2 * math.pi),
                      1.0,
                    ],
                  ),
                ),
              );
            },
          ),

          // Particle Background
          AnimatedBuilder(
            animation: _particleController,
            builder: (context, child) {
              return CustomPaint(
                painter: OnboardingParticlesPainter(
                  _particleController.value,
                  _onboardingData[_currentPage].color,
                ),
                size: size,
              );
            },
          ),

          // Glassmorphism Overlay
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white.withOpacity(0.1),
                    Colors.white.withOpacity(0.05),
                  ],
                ),
              ),
            ),
          ),

          // Main Content
          SafeArea(
            child: Column(
              children: [
                // Top Section with Skip and Indicators
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 60),

                      // Modern Page Indicators
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: GlassmorphismUtils.getGlassDecoration(
                          opacity: 0.2,
                          blur: 10,
                        ),
                        child: Row(
                          children: List.generate(
                            _onboardingData.length,
                            (index) => AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              width: _currentPage == index ? 32 : 12,
                              height: 12,
                              decoration: BoxDecoration(
                                gradient: _currentPage == index
                                    ? LinearGradient(
                                        colors: _onboardingData[_currentPage].gradient,
                                      )
                                    : null,
                                color: _currentPage != index
                                    ? Colors.white.withOpacity(0.4)
                                    : null,
                                borderRadius: BorderRadius.circular(6),
                                boxShadow: _currentPage == index
                                    ? [
                                        BoxShadow(
                                          color: _onboardingData[_currentPage]
                                              .color
                                              .withOpacity(0.4),
                                          blurRadius: 8,
                                          spreadRadius: 2,
                                        ),
                                      ]
                                    : null,
                              ),
                            ).animate().scale(duration: 300.ms),
                          ),
                        ),
                      ),

                      // Skip Button
                      Container(
                        decoration: GlassmorphismUtils.getGlassDecoration(
                          opacity: 0.2,
                          blur: 10,
                        ),
                        child: TextButton(
                          onPressed: () => _navigateToLogin(),
                          child: const Text(
                            'Skip',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // PageView with 3D Effect
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                    },
                    itemCount: _onboardingData.length,
                    itemBuilder: (context, index) {
                      return _buildModernOnboardingPage(_onboardingData[index], index);
                    },
                  ),
                ),

                // Modern Bottom Navigation
                Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Previous Button
                      if (_currentPage > 0)
                        Container(
                          decoration: GlassmorphismUtils.getGlassDecoration(
                            opacity: 0.2,
                            blur: 10,
                          ),
                          child: TextButton(
                            onPressed: () {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOutCubic,
                              );
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.arrow_back_ios,
                                  size: 18,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Previous',
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ).animate().slideX(begin: -0.3, duration: 300.ms)
                      else
                        const SizedBox(width: 100),

                      // Next/Get Started Button
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: _onboardingData[_currentPage].gradient,
                          ),
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: _onboardingData[_currentPage]
                                  .color
                                  .withOpacity(0.4),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: () {
                            if (_currentPage < _onboardingData.length - 1) {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 500),
                                curve: Curves.easeInOutCubic,
                              );
                            } else {
                              _navigateToLogin();
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 32,
                              vertical: 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _currentPage < _onboardingData.length - 1
                                    ? 'Next'
                                    : 'Get Started',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Icon(
                                Icons.arrow_forward_ios,
                                size: 18,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ).animate().scale(duration: 200.ms).shimmer(
                            duration: 2000.ms,
                            color: Colors.white.withOpacity(0.3),
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernOnboardingPage(OnboardingData data, int index) {
    return AnimatedBuilder(
      animation: Listenable.merge([_floatingController, _particleController]),
      builder: (context, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingLarge,
            vertical: AppDimensions.paddingMedium,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 3D Animated Icon Container
              Transform.translate(
                offset: Offset(0, _floatingAnimation.value),
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        data.gradient[0].withOpacity(0.3),
                        data.gradient[1].withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Outer Ring
                      Container(
                        width: 180,
                        height: 180,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 2,
                          ),
                        ),
                      ),

                      // Middle Ring
                      Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: data.color.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                      ),

                      // Inner Glassmorphism Container
                      Container(
                        width: 120,
                        height: 120,
                        decoration: GlassmorphismUtils.getGlassDecoration(
                          opacity: 0.2,
                          blur: 15,
                          borderRadius: 60,
                        ).copyWith(
                          boxShadow: [
                            BoxShadow(
                              color: data.color.withOpacity(0.3),
                              blurRadius: 30,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: Transform.scale(
                          scale: 1.0 + 0.1 * math.sin(_particleController.value * 2 * math.pi),
                          child: Icon(
                            data.icon,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
                  .animate()
                  .scale(
                    duration: 1000.ms,
                    curve: Curves.elasticOut,
                  )
                  .fadeIn(duration: 800.ms),

              const SizedBox(height: 60),

              // Modern Title with Glassmorphism
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                decoration: GlassmorphismUtils.getGlassDecoration(
                  opacity: 0.15,
                  blur: 10,
                ),
                child: Text(
                  data.title,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 1,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 3,
                        color: Colors.black26,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              )
                  .animate()
                  .fadeIn(duration: 800.ms, delay: 300.ms)
                  .slideY(begin: 0.3, end: 0),

              const SizedBox(height: 30),

              // Modern Description
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: GlassmorphismUtils.getGlassDecoration(
                  opacity: 0.1,
                  blur: 8,
                ),
                child: Text(
                  data.description,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    height: 1.6,
                    fontWeight: FontWeight.w300,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
                  .animate()
                  .fadeIn(duration: 800.ms, delay: 500.ms)
                  .slideY(begin: 0.3, end: 0),

              const SizedBox(height: 50),

              // 3D Floating Elements
              SizedBox(
                height: 120,
                child: Stack(
                  children: List.generate(6, (i) {
                    final positions = [
                      const Offset(0.1, 0.2),
                      const Offset(0.85, 0.1),
                      const Offset(0.2, 0.7),
                      const Offset(0.8, 0.8),
                      const Offset(0.5, 0.1),
                      const Offset(0.6, 0.6),
                    ];
                    final icons = [
                      Icons.star,
                      Icons.diamond,
                      Icons.monetization_on,
                      Icons.auto_awesome,
                      Icons.flash_on,
                      Icons.favorite,
                    ];

                    return Positioned(
                      left: MediaQuery.of(context).size.width * positions[i].dx,
                      top: 120 * positions[i].dy,
                      child: Transform.translate(
                        offset: Offset(
                          10 * math.sin(_particleController.value * 2 * math.pi + i),
                          5 * math.cos(_particleController.value * 2 * math.pi + i),
                        ),
                        child: Transform.scale(
                          scale: 0.8 + 0.4 * math.sin(
                            _particleController.value * 2 * math.pi + i * 0.5,
                          ),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  data.color.withOpacity(0.3),
                                  data.color.withOpacity(0.1),
                                ],
                              ),
                            ),
                            child: Icon(
                              icons[i],
                              color: Colors.white.withOpacity(0.6),
                              size: 20 + i * 2,
                            ),
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _navigateToLogin() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const LoginScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOutCubic,
              )),
              child: child,
            ),
          );
        },
        transitionDuration: const Duration(milliseconds: 800),
      ),
    );
  }
}

class OnboardingData {
  final IconData icon;
  final String title;
  final String description;
  final Color color;
  final List<Color> gradient;
  final String? lottieAsset;

  OnboardingData({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
    required this.gradient,
    this.lottieAsset,
  });
}

// Custom Painter for Onboarding Particles
class OnboardingParticlesPainter extends CustomPainter {
  final double animationValue;
  final Color primaryColor;

  OnboardingParticlesPainter(this.animationValue, this.primaryColor);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Create floating particles
    for (int i = 0; i < 30; i++) {
      final progress = (animationValue + i * 0.1) % 1.0;
      final x = (size.width * (i * 0.1 + progress * 0.3)) % size.width;
      final y = (size.height * (i * 0.07 + progress * 0.5)) % size.height;
      final radius = 1.0 + (i % 4).toDouble();
      final opacity = 0.1 + 0.2 * math.sin(progress * 2 * math.pi);

      paint.color = primaryColor.withOpacity(opacity);
      canvas.drawCircle(Offset(x, y), radius, paint);
    }

    // Create larger floating elements
    for (int i = 0; i < 8; i++) {
      final progress = (animationValue * 0.5 + i * 0.2) % 1.0;
      final x = size.width * (0.1 + 0.8 * progress);
      final y = size.height * (0.2 + 0.6 * math.sin(progress * math.pi));
      final radius = 3.0 + (i % 3).toDouble();
      final opacity = 0.05 + 0.1 * math.sin(progress * 2 * math.pi);

      paint.color = Colors.white.withOpacity(opacity);
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
