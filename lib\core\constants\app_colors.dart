import 'package:flutter/material.dart';

class AppColors {
  // Modern Gradient Primary Colors - Futuristic Theme
  static const Color primary = Color(0xFF00D4FF);
  static const Color primaryDark = Color(0xFF0099CC);
  static const Color primaryLight = Color(0xFF33E0FF);
  static const Color primaryAccent = Color(0xFF7C4DFF);

  // Neon Accent Colors
  static const Color accent = Color(0xFFFF3D71);
  static const Color secondary = Color(0xFF7C4DFF);
  static const Color accentLight = Color(0xFFFF6B9D);
  static const Color accentSecondary = Color(0xFF00E676);
  static const Color accentTertiary = Color(0xFFFFAB00);

  // Success & Earning Colors - Enhanced
  static const Color success = Color(0xFF00E676);
  static const Color earning = Color(0xFF4CAF50);
  static const Color gold = Color(0xFFFFD700);
  static const Color coin = Color(0xFFFFC107);
  static const Color diamond = Color(0xFFB39DDB);
  static const Color platinum = Color(0xFFE1F5FE);

  // Modern Background Colors - Glassmorphism Ready
  static const Color background = Color(0xFFF0F4F8);
  static const Color backgroundGlass = Color(0xFFFAFBFC);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8FAFC);
  static const Color surfaceGlass = Color(0xFFFFFFFF);

  // Dark Theme Colors - Enhanced
  static const Color backgroundDark = Color(0xFF0A0E1A);
  static const Color backgroundDarkGlass = Color(0xFF1A1F2E);
  static const Color surfaceDark = Color(0xFF1E2328);
  static const Color surfaceVariantDark = Color(0xFF2A2F36);
  static const Color surfaceDarkGlass = Color(0xFF252A32);

  // Modern Text Colors
  static const Color textPrimary = Color(0xFF1A202C);
  static const Color textSecondary = Color(0xFF4A5568);
  static const Color textHint = Color(0xFF9CA3AF);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnGlass = Color(0xFF2D3748);

  // Dark Text Colors - Enhanced
  static const Color textPrimaryDark = Color(0xFFF7FAFC);
  static const Color textSecondaryDark = Color(0xFFE2E8F0);
  static const Color textHintDark = Color(0xFFA0AEC0);

  // Modern Gradient Collections - 3D & Glassmorphism Ready
  static const List<Color> primaryGradient = [
    Color(0xFF00D4FF),
    Color(0xFF7C4DFF),
    Color(0xFF00E676),
  ];

  static const List<Color> earningGradient = [
    Color(0xFF00E676),
    Color(0xFF4CAF50),
    Color(0xFF2E7D32),
  ];

  static const List<Color> goldGradient = [
    Color(0xFFFFD700),
    Color(0xFFFFC107),
    Color(0xFFFF8F00),
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFFF0F4F8),
    Color(0xFFE2E8F0),
    Color(0xFFEDF2F7),
  ];

  static const List<Color> glassGradient = [
    Color(0x40FFFFFF),
    Color(0x20FFFFFF),
    Color(0x10FFFFFF),
  ];

  static const List<Color> neonGradient = [
    Color(0xFFFF3D71),
    Color(0xFF00D4FF),
    Color(0xFF7C4DFF),
  ];

  // Status Colors - Enhanced
  static const Color error = Color(0xFFFF5252);
  static const Color warning = Color(0xFFFFAB00);
  static const Color info = Color(0xFF00D4FF);
  static const Color successBright = Color(0xFF00E676);

  // Glassmorphism Colors
  static const Color glassWhite = Color(0x40FFFFFF);
  static const Color glassDark = Color(0x40000000);
  static const Color glassBlur = Color(0x20FFFFFF);
  static const Color glassBorder = Color(0x30FFFFFF);

  // Shimmer Colors - Enhanced
  static const Color shimmerBase = Color(0xFFE2E8F0);
  static const Color shimmerHighlight = Color(0xFFF7FAFC);
  static const Color shimmerBaseDark = Color(0xFF2D3748);
  static const Color shimmerHighlightDark = Color(0xFF4A5568);

  // Card Colors - Glassmorphism
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardGlass = Color(0x80FFFFFF);
  static const Color cardShadow = Color(0x1A000000);
  static const Color cardGlassShadow = Color(0x40000000);

  // Border Colors - Modern
  static const Color border = Color(0xFFE2E8F0);
  static const Color borderLight = Color(0xFFF7FAFC);
  static const Color borderGlass = Color(0x30FFFFFF);
  static const Color borderNeon = Color(0xFF00D4FF);

  // Video & Game Colors - Enhanced
  static const Color videoBackground = Color(0xFF0A0E1A);
  static const Color gameBackground = Color(0xFF1A1F2E);
  static const Color gamingNeon = Color(0xFF7C4DFF);
  static const Color gamingAccent = Color(0xFFFF3D71);

  // 3D & Depth Colors
  static const Color shadow3D = Color(0x40000000);
  static const Color highlight3D = Color(0x60FFFFFF);
  static const Color depth1 = Color(0xFFF7FAFC);
  static const Color depth2 = Color(0xFFEDF2F7);
  static const Color depth3 = Color(0xFFE2E8F0);

  // Particle & Effect Colors
  static const Color particleGold = Color(0xFFFFD700);
  static const Color particleBlue = Color(0xFF00D4FF);
  static const Color particlePurple = Color(0xFF7C4DFF);
  static const Color particleGreen = Color(0xFF00E676);
  static const Color particlePink = Color(0xFFFF3D71);
  
  // Social Colors
  static const Color facebook = Color(0xFF1877F2);
  static const Color twitter = Color(0xFF1DA1F2);
  static const Color instagram = Color(0xFFE4405F);
  static const Color youtube = Color(0xFFFF0000);
  static const Color whatsapp = Color(0xFF25D366);
}
