import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../../../core/constants/app_colors.dart';
import '../home/<USER>';

class PremiumLoginScreen extends StatefulWidget {
  const PremiumLoginScreen({super.key});

  @override
  State<PremiumLoginScreen> createState() => _PremiumLoginScreenState();
}

class _PremiumLoginScreenState extends State<PremiumLoginScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _glowController;
  late AnimationController _particleController;
  late AnimationController _formController;
  
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _particleController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat();
    
    _formController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    // Start entrance animation
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) _formController.forward();
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _glowController.dispose();
    _particleController.dispose();
    _formController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.topLeft,
            radius: 1.5,
            colors: [
              AppColors.backgroundDark,
              AppColors.backgroundGlass,
              AppColors.background,
            ],
          ),
        ),
        child: Stack(
          children: [
            // Animated Background Particles
            ...List.generate(25, (index) {
              return AnimatedBuilder(
                animation: _particleController,
                builder: (context, child) {
                  final progress = (_particleController.value + index * 0.1) % 1.0;
                  final x = size.width * (0.1 + 0.8 * math.sin(index * 0.7 + progress * 2 * math.pi));
                  final y = size.height * (1.2 - progress * 1.4);
                  final opacity = (1 - progress) * 0.3;
                  final scale = 0.3 + 0.7 * math.sin(progress * math.pi);
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Transform.scale(
                      scale: scale,
                      child: Container(
                        width: 4 + (index % 6) * 2,
                        height: 4 + (index % 6) * 2,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              AppColors.primary.withOpacity(opacity),
                              AppColors.accent.withOpacity(opacity * 0.5),
                              Colors.transparent,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withOpacity(opacity * 0.5),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Floating Orbs
            ...List.generate(8, (index) {
              return AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  final progress = (_backgroundController.value + index * 0.2) % 1.0;
                  final x = size.width * (0.2 + 0.6 * math.cos(index * 0.8 + progress * 2 * math.pi));
                  final y = size.height * (0.2 + 0.6 * math.sin(index * 0.5 + progress * 2 * math.pi));
                  final opacity = 0.1 + 0.1 * math.sin(progress * 2 * math.pi);
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Container(
                      width: 80 + (index % 3) * 40,
                      height: 80 + (index % 3) * 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            AppColors.primary.withOpacity(opacity),
                            AppColors.accent.withOpacity(opacity * 0.3),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Main Content
            SafeArea(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 20 : 32,
                  vertical: isSmallScreen ? 20 : 40,
                ),
                child: Column(
                  children: [
                    SizedBox(height: isSmallScreen ? 40 : 80),
                    
                    // Logo Section with Glow Effect
                    AnimatedBuilder(
                      animation: _glowController,
                      builder: (context, child) {
                        return Container(
                          width: isSmallScreen ? 140 : 180,
                          height: isSmallScreen ? 140 : 180,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: AppColors.primaryGradient,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withOpacity(0.6 + 0.4 * _glowController.value),
                                blurRadius: 40 + 20 * _glowController.value,
                                spreadRadius: 10 + 5 * _glowController.value,
                              ),
                              BoxShadow(
                                color: AppColors.accent.withOpacity(0.3 + 0.2 * _glowController.value),
                                blurRadius: 60 + 30 * _glowController.value,
                                spreadRadius: 15 + 10 * _glowController.value,
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.monetization_on_rounded,
                            size: isSmallScreen ? 70 : 90,
                            color: AppColors.textOnPrimary,
                          ),
                        ).animate()
                          .fadeIn(duration: 1000.ms)
                          .scale(delay: 200.ms, duration: 800.ms)
                          .then()
                          .shimmer(duration: 2000.ms, color: AppColors.primary.withOpacity(0.3));
                      },
                    ),
                    
                    SizedBox(height: isSmallScreen ? 30 : 50),
                    
                    // App Name with Gradient Text
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: AppColors.primaryGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds),
                      child: Text(
                        'Easy Money',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 48 : 64,
                          fontWeight: FontWeight.w900,
                          color: Colors.white,
                          letterSpacing: 2,
                          shadows: [
                            Shadow(
                              color: AppColors.primary.withOpacity(0.5),
                              blurRadius: 20,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                      ),
                    ).animate()
                      .fadeIn(delay: 600.ms, duration: 1000.ms)
                      .slideY(begin: 0.3, delay: 600.ms, duration: 1000.ms),
                    
                    SizedBox(height: isSmallScreen ? 15 : 25),
                    
                    Text(
                      'Your Gateway to Financial Freedom',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 16 : 20,
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 1,
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                      .fadeIn(delay: 800.ms, duration: 1000.ms)
                      .slideY(begin: 0.3, delay: 800.ms, duration: 1000.ms),
                    
                    SizedBox(height: isSmallScreen ? 60 : 80),
                    
                    // Premium Login Form
                    Container(
                      padding: EdgeInsets.all(isSmallScreen ? 28 : 40),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.surfaceGlass.withOpacity(0.9),
                            AppColors.surfaceVariant.withOpacity(0.7),
                            AppColors.surface.withOpacity(0.8),
                          ],
                        ),
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.3),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.2),
                            blurRadius: 40,
                            spreadRadius: 8,
                          ),
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 2,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Welcome Text
                          ShaderMask(
                            shaderCallback: (bounds) => LinearGradient(
                              colors: AppColors.primaryGradient,
                            ).createShader(bounds),
                            child: Text(
                              'Welcome Back!',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 28 : 36,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                                letterSpacing: 1,
                              ),
                            ),
                          ),
                          
                          SizedBox(height: isSmallScreen ? 12 : 16),
                          
                          Text(
                            'Sign in to continue your earning journey',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 16 : 18,
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          
                          SizedBox(height: isSmallScreen ? 40 : 50),
                          
                          // Email Field
                          _buildPremiumTextField(
                            controller: _emailController,
                            label: 'Email Address',
                            icon: Icons.email_outlined,
                            keyboardType: TextInputType.emailAddress,
                            isSmallScreen: isSmallScreen,
                          ),
                          
                          SizedBox(height: isSmallScreen ? 24 : 30),
                          
                          // Password Field
                          _buildPremiumTextField(
                            controller: _passwordController,
                            label: 'Password',
                            icon: Icons.lock_outline,
                            isPassword: true,
                            isPasswordVisible: _isPasswordVisible,
                            onTogglePassword: () {
                              setState(() {
                                _isPasswordVisible = !_isPasswordVisible;
                              });
                            },
                            isSmallScreen: isSmallScreen,
                          ),
                          
                          SizedBox(height: isSmallScreen ? 20 : 25),
                          
                          // Forgot Password
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: () {},
                              child: ShaderMask(
                                shaderCallback: (bounds) => LinearGradient(
                                  colors: AppColors.primaryGradient,
                                ).createShader(bounds),
                                child: const Text(
                                  'Forgot Password?',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          
                          SizedBox(height: isSmallScreen ? 30 : 40),
                          
                          // Premium Login Button
                          _buildPremiumButton(isSmallScreen),
                        ],
                      ),
                    ).animate()
                      .fadeIn(delay: 1000.ms, duration: 1200.ms)
                      .slideY(begin: 0.4, delay: 1000.ms, duration: 1200.ms)
                      .then()
                      .shimmer(duration: 3000.ms, color: AppColors.primary.withOpacity(0.1)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool isPassword = false,
    bool isPasswordVisible = false,
    VoidCallback? onTogglePassword,
    required bool isSmallScreen,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.surfaceVariant.withOpacity(0.8),
            AppColors.surface.withOpacity(0.6),
          ],
        ),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 15,
            spreadRadius: 2,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: isPassword && !isPasswordVisible,
        style: TextStyle(
          color: AppColors.textPrimary,
          fontSize: isSmallScreen ? 16 : 18,
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.w500,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: AppColors.primaryGradient,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Icon(
              icon,
              color: AppColors.textOnPrimary,
              size: isSmallScreen ? 20 : 22,
            ),
          ),
          suffixIcon: isPassword
              ? IconButton(
                  onPressed: onTogglePassword,
                  icon: Icon(
                    isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.primary,
                    size: isSmallScreen ? 22 : 24,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(isSmallScreen ? 20 : 24),
        ),
      ),
    ).animate()
      .fadeIn(duration: 600.ms)
      .slideX(begin: -0.3, duration: 600.ms);
  }

  Widget _buildPremiumButton(bool isSmallScreen) {
    return AnimatedBuilder(
      animation: _glowController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: isSmallScreen ? 60 : 70,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: AppColors.primaryGradient,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.5 + 0.3 * _glowController.value),
                blurRadius: 25 + 15 * _glowController.value,
                spreadRadius: 5 + 3 * _glowController.value,
              ),
              BoxShadow(
                color: AppColors.accent.withOpacity(0.3 + 0.2 * _glowController.value),
                blurRadius: 35 + 20 * _glowController.value,
                spreadRadius: 8 + 5 * _glowController.value,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleLogin,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    width: 28,
                    height: 28,
                    child: CircularProgressIndicator(
                      color: AppColors.textOnPrimary,
                      strokeWidth: 3,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.login_rounded,
                        color: AppColors.textOnPrimary,
                        size: isSmallScreen ? 24 : 28,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Sign In',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 18 : 22,
                          fontWeight: FontWeight.w800,
                          color: AppColors.textOnPrimary,
                          letterSpacing: 1,
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    ).animate()
      .fadeIn(duration: 800.ms)
      .scale(delay: 200.ms, duration: 600.ms)
      .then()
      .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3));
  }

  void _handleLogin() async {
    setState(() {
      _isLoading = true;
    });

    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const PremiumHomeScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 0.3),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOutCubic,
                )),
                child: child,
              ),
            );
          },
          transitionDuration: const Duration(milliseconds: 1200),
        ),
      );
    }
  }
}
