import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shimmer/shimmer.dart';
import '../../core/constants/app_colors.dart';
import 'themes/neon_cyberpunk/neon_splash_screen.dart';
import 'themes/luxury_gold/luxury_splash_screen.dart';
import 'themes/ocean_blue/ocean_splash_screen.dart';
import 'themes/sunset_orange/sunset_splash_screen.dart';
import 'themes/forest_green/forest_splash_screen.dart';
import 'themes/royal_purple/royal_splash_screen.dart';
import 'themes/minimal_white/minimal_splash_screen.dart';
import 'themes/dark_red/dark_red_splash_screen.dart';
import 'themes/cosmic_space/cosmic_splash_screen.dart';
import 'themes/rainbow_gradient/rainbow_splash_screen.dart';

class DesignSelectionScreen extends StatefulWidget {
  const DesignSelectionScreen({super.key});

  @override
  State<DesignSelectionScreen> createState() => _DesignSelectionScreenState();
}

class _DesignSelectionScreenState extends State<DesignSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late PageController _pageController;
  int _currentIndex = 0;

  final List<DesignTheme> _themes = [
    DesignTheme(
      name: 'Neon Cyberpunk',
      description: 'Futuristic neon lights with cyberpunk aesthetics',
      colors: [Color(0xFF00FFFF), Color(0xFFFF00FF), Color(0xFF00FF00)],
      screen: const NeonSplashScreen(),
      icon: Icons.flash_on,
    ),
    DesignTheme(
      name: 'Luxury Gold',
      description: 'Premium gold and black luxury design',
      colors: [Color(0xFFFFD700), Color(0xFFFF8C00), Color(0xFF000000)],
      screen: const LuxurySplashScreen(),
      icon: Icons.diamond,
    ),
    DesignTheme(
      name: 'Ocean Blue',
      description: 'Calming ocean waves with blue gradients',
      colors: [Color(0xFF0077BE), Color(0xFF00A8CC), Color(0xFF87CEEB)],
      screen: const OceanSplashScreen(),
      icon: Icons.waves,
    ),
    DesignTheme(
      name: 'Sunset Orange',
      description: 'Warm sunset colors with orange and pink',
      colors: [Color(0xFFFF6B35), Color(0xFFFF8E53), Color(0xFFFF006E)],
      screen: const SunsetSplashScreen(),
      icon: Icons.wb_sunny,
    ),
    DesignTheme(
      name: 'Forest Green',
      description: 'Natural forest theme with green gradients',
      colors: [Color(0xFF228B22), Color(0xFF32CD32), Color(0xFF90EE90)],
      screen: const ForestSplashScreen(),
      icon: Icons.nature,
    ),
    DesignTheme(
      name: 'Royal Purple',
      description: 'Elegant royal purple with gold accents',
      colors: [Color(0xFF6A0DAD), Color(0xFF9370DB), Color(0xFFFFD700)],
      screen: const RoyalSplashScreen(),
      icon: Icons.star_border,
    ),
    DesignTheme(
      name: 'Minimal White',
      description: 'Clean minimal design with white and gray',
      colors: [Color(0xFFFFFFFF), Color(0xFFF5F5F5), Color(0xFF333333)],
      screen: const MinimalSplashScreen(),
      icon: Icons.minimize,
    ),
    DesignTheme(
      name: 'Dark Red',
      description: 'Bold dark red with black accents',
      colors: [Color(0xFF8B0000), Color(0xFFDC143C), Color(0xFF000000)],
      screen: const DarkRedSplashScreen(),
      icon: Icons.favorite,
    ),
    DesignTheme(
      name: 'Cosmic Space',
      description: 'Space theme with stars and galaxies',
      colors: [Color(0xFF191970), Color(0xFF4B0082), Color(0xFF9400D3)],
      screen: const CosmicSplashScreen(),
      icon: Icons.star,
    ),
    DesignTheme(
      name: 'Rainbow Gradient',
      description: 'Vibrant rainbow colors with smooth gradients',
      colors: [Color(0xFFFF0000), Color(0xFF00FF00), Color(0xFF0000FF)],
      screen: const RainbowSplashScreen(),
      icon: Icons.color_lens,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _controller.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF0A0A0F),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.white,
                      highlightColor: AppColors.primary,
                      child: Text(
                        'Choose Your Design',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.w900,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'Select the perfect UI theme for your Easy Money app',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              // Theme Grid
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.all(20),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 15,
                    mainAxisSpacing: 15,
                  ),
                  itemCount: _themes.length,
                  itemBuilder: (context, index) {
                    final theme = _themes[index];
                    return _buildThemeCard(theme, index);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemeCard(DesignTheme theme, int index) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => theme.screen,
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 800),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: theme.colors,
          ),
          boxShadow: [
            BoxShadow(
              color: theme.colors.first.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Background Pattern
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withOpacity(0.1),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.2),
                    ),
                    child: Icon(
                      theme.icon,
                      size: 30,
                      color: Colors.white,
                    ),
                  ).animate()
                    .scale(duration: 600.ms, curve: Curves.elasticOut)
                    .then()
                    .shimmer(duration: 2000.ms),
                  
                  const SizedBox(height: 16),
                  
                  // Theme Name
                  Text(
                    theme.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Description
                  Text(
                    theme.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.8),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ).animate()
        .fadeIn(duration: 600.ms, delay: (index * 100).ms)
        .slideY(begin: 0.3, duration: 600.ms, delay: (index * 100).ms),
    );
  }
}

class DesignTheme {
  final String name;
  final String description;
  final List<Color> colors;
  final Widget screen;
  final IconData icon;

  DesignTheme({
    required this.name,
    required this.description,
    required this.colors,
    required this.screen,
    required this.icon,
  });
}
