import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../widgets/game_card.dart';

class GamesScreen extends StatefulWidget {
  const GamesScreen({super.key});

  @override
  State<GamesScreen> createState() => _GamesScreenState();
}

class _GamesScreenState extends State<GamesScreen> {
  final List<GameData> _games = [
    GameData(
      id: '1',
      title: 'Puzzle Master',
      description: 'Solve challenging puzzles and earn coins',
      thumbnail: 'https://picsum.photos/300/200?random=10',
      rewardPerLevel: 0.25,
      category: 'Puzzle',
      difficulty: 'Easy',
      playTime: '5-10 min',
      totalLevels: 50,
      completedLevels: 12,
      rating: 4.8,
      players: '2.1M',
      color: AppColors.primary,
      icon: Icons.extension,
    ),
    GameData(
      id: '2',
      title: 'Word Hunt',
      description: 'Find hidden words and boost your vocabulary',
      thumbnail: 'https://picsum.photos/300/200?random=11',
      rewardPerLevel: 0.30,
      category: 'Word',
      difficulty: 'Medium',
      playTime: '3-8 min',
      totalLevels: 100,
      completedLevels: 25,
      rating: 4.6,
      players: '1.8M',
      color: AppColors.accent,
      icon: Icons.text_fields,
    ),
    GameData(
      id: '3',
      title: 'Math Challenge',
      description: 'Test your math skills with fun challenges',
      thumbnail: 'https://picsum.photos/300/200?random=12',
      rewardPerLevel: 0.40,
      category: 'Education',
      difficulty: 'Hard',
      playTime: '2-5 min',
      totalLevels: 75,
      completedLevels: 8,
      rating: 4.7,
      players: '950K',
      color: AppColors.earning,
      icon: Icons.calculate,
    ),
    GameData(
      id: '4',
      title: 'Memory Match',
      description: 'Improve your memory with card matching',
      thumbnail: 'https://picsum.photos/300/200?random=13',
      rewardPerLevel: 0.20,
      category: 'Memory',
      difficulty: 'Easy',
      playTime: '3-7 min',
      totalLevels: 60,
      completedLevels: 18,
      rating: 4.5,
      players: '1.2M',
      color: AppColors.info,
      icon: Icons.psychology,
    ),
    GameData(
      id: '5',
      title: 'Color Switch',
      description: 'Fast-paced color matching game',
      thumbnail: 'https://picsum.photos/300/200?random=14',
      rewardPerLevel: 0.15,
      category: 'Arcade',
      difficulty: 'Medium',
      playTime: '1-3 min',
      totalLevels: 200,
      completedLevels: 45,
      rating: 4.4,
      players: '3.5M',
      color: AppColors.warning,
      icon: Icons.palette,
    ),
    GameData(
      id: '6',
      title: 'Quiz Master',
      description: 'Answer trivia questions and learn',
      thumbnail: 'https://picsum.photos/300/200?random=15',
      rewardPerLevel: 0.50,
      category: 'Trivia',
      difficulty: 'Hard',
      playTime: '5-12 min',
      totalLevels: 40,
      completedLevels: 5,
      rating: 4.9,
      players: '800K',
      color: AppColors.gold,
      icon: Icons.quiz,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // Featured Game
              _buildFeaturedGame(),
              
              // Games Grid
              Expanded(
                child: _buildGamesGrid(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Play & Earn',
                style: AppTextStyles.h3.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 600.ms),
              
              const SizedBox(height: 4),
              
              Text(
                'Have fun while earning coins',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
            ],
          ),
          
          // Gaming earnings
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [AppColors.gameBackground, AppColors.primary],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.games,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '\$6.25',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ).animate().scale(duration: 600.ms, delay: 400.ms),
        ],
      ),
    );
  }

  Widget _buildFeaturedGame() {
    final featuredGame = _games.first;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            featuredGame.color.withOpacity(0.1),
            featuredGame.color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: featuredGame.color.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: featuredGame.color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: featuredGame.color,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'FEATURED',
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                  ),
                ),
              ),
              const Spacer(),
              Icon(
                featuredGame.icon,
                color: featuredGame.color,
                size: 32,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Text(
            featuredGame.title,
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            featuredGame.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              _buildGameStat(
                Icons.star,
                featuredGame.rating.toString(),
                AppColors.gold,
              ),
              const SizedBox(width: 16),
              _buildGameStat(
                Icons.people,
                featuredGame.players,
                AppColors.info,
              ),
              const SizedBox(width: 16),
              _buildGameStat(
                Icons.monetization_on,
                '\$${featuredGame.rewardPerLevel.toStringAsFixed(2)}',
                AppColors.earning,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _playGame(featuredGame),
              style: ElevatedButton.styleFrom(
                backgroundColor: featuredGame.color,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Play Now',
                style: AppTextStyles.buttonMedium,
              ),
            ),
          ),
        ],
      ),
    ).animate().slideY(duration: 800.ms, delay: 600.ms);
  }

  Widget _buildGameStat(IconData icon, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          value,
          style: AppTextStyles.bodySmall.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildGamesGrid() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'All Games',
            style: AppTextStyles.h5.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 800.ms),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: _games.length,
              itemBuilder: (context, index) {
                final game = _games[index];
                return GameCard(
                  game: game,
                  onTap: () => _playGame(game),
                ).animate().scale(
                  duration: 600.ms,
                  delay: (1000 + index * 100).ms,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _playGame(GameData game) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              game.icon,
              color: game.color,
              size: 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                game.title,
                style: AppTextStyles.h5,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              game.description,
              style: AppTextStyles.bodyMedium,
            ),
            
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Reward per level:',
                  style: AppTextStyles.bodyMedium,
                ),
                Text(
                  '\$${game.rewardPerLevel.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.earning,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Progress:',
                  style: AppTextStyles.bodyMedium,
                ),
                Text(
                  '${game.completedLevels}/${game.totalLevels}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: game.color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showGameResult(game);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: game.color,
            ),
            child: Text(
              'Play',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _showGameResult(GameData game) {
    // Simulate game completion
    final earned = game.rewardPerLevel;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: game.color,
                shape: BoxShape.circle,
              ),
              child: Icon(
                game.icon,
                color: Colors.white,
                size: 40,
              ),
            ).animate().scale(duration: 600.ms, curve: Curves.elasticOut),
            
            const SizedBox(height: 20),
            
            Text(
              'Level Complete!',
              style: AppTextStyles.h5.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'You earned \$${earned.toStringAsFixed(2)}',
              style: AppTextStyles.h4.copyWith(
                color: AppColors.earning,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Continue',
                style: AppTextStyles.buttonMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GameData {
  final String id;
  final String title;
  final String description;
  final String thumbnail;
  final double rewardPerLevel;
  final String category;
  final String difficulty;
  final String playTime;
  final int totalLevels;
  final int completedLevels;
  final double rating;
  final String players;
  final Color color;
  final IconData icon;

  GameData({
    required this.id,
    required this.title,
    required this.description,
    required this.thumbnail,
    required this.rewardPerLevel,
    required this.category,
    required this.difficulty,
    required this.playTime,
    required this.totalLevels,
    required this.completedLevels,
    required this.rating,
    required this.players,
    required this.color,
    required this.icon,
  });
}
