import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../../../core/constants/app_colors.dart';

class PremiumHomeScreen extends StatefulWidget {
  const PremiumHomeScreen({super.key});

  @override
  State<PremiumHomeScreen> createState() => _PremiumHomeScreenState();
}

class _PremiumHomeScreenState extends State<PremiumHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _glowController;
  late AnimationController _particleController;
  
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const _PremiumHomePage(),
    const _PremiumTasksPage(),
    const _PremiumVideosPage(),
    const _PremiumGamesPage(),
    const _PremiumProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _particleController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _glowController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      extendBody: true,
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.topCenter,
            radius: 1.2,
            colors: [
              AppColors.backgroundDark,
              AppColors.background,
              AppColors.backgroundGlass,
            ],
          ),
        ),
        child: Stack(
          children: [
            // Floating Particles Background
            ...List.generate(30, (index) {
              return AnimatedBuilder(
                animation: _particleController,
                builder: (context, child) {
                  final progress = (_particleController.value + index * 0.1) % 1.0;
                  final x = size.width * (0.1 + 0.8 * math.sin(index * 0.6 + progress * 2 * math.pi));
                  final y = size.height * (1.2 - progress * 1.4);
                  final opacity = (1 - progress) * 0.2;
                  final scale = 0.2 + 0.8 * math.sin(progress * math.pi);
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Transform.scale(
                      scale: scale,
                      child: Container(
                        width: 3 + (index % 5) * 2,
                        height: 3 + (index % 5) * 2,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              AppColors.primary.withOpacity(opacity),
                              AppColors.accent.withOpacity(opacity * 0.5),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            }),
            
            // Main Content
            _pages[_selectedIndex],
          ],
        ),
      ),
      bottomNavigationBar: Container(
        height: 100,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.surface.withOpacity(0.95),
              AppColors.surfaceVariant.withOpacity(0.9),
              AppColors.surfaceGlass.withOpacity(0.85),
            ],
          ),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.2),
              blurRadius: 25,
              spreadRadius: 5,
            ),
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPremiumNavItem(Icons.home_rounded, 'Home', 0),
              _buildPremiumNavItem(Icons.task_alt_rounded, 'Tasks', 1),
              _buildPremiumNavItem(Icons.play_circle_rounded, 'Videos', 2),
              _buildPremiumNavItem(Icons.games_rounded, 'Games', 3),
              _buildPremiumNavItem(Icons.person_rounded, 'Profile', 4),
            ],
          ),
        ),
      ).animate()
        .fadeIn(delay: 1000.ms, duration: 800.ms)
        .slideY(begin: 1, delay: 1000.ms, duration: 800.ms),
    );
  }

  Widget _buildPremiumNavItem(IconData icon, String label, int index) {
    final isSelected = _selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      child: AnimatedBuilder(
        animation: _glowController,
        builder: (context, child) {
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: isSelected
                  ? LinearGradient(
                      colors: AppColors.primaryGradient.map((c) => c.withOpacity(0.3)).toList(),
                    )
                  : null,
              border: isSelected
                  ? Border.all(
                      color: AppColors.primary.withOpacity(0.5),
                      width: 1.5,
                    )
                  : null,
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.3 + 0.2 * _glowController.value),
                        blurRadius: 15 + 10 * _glowController.value,
                        spreadRadius: 3 + 2 * _glowController.value,
                      ),
                    ]
                  : null,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: isSelected ? AppColors.primary : AppColors.textSecondary,
                  size: isSelected ? 28 : 24,
                ),
                const SizedBox(height: 6),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                    color: isSelected ? AppColors.primary : AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    ).animate(target: isSelected ? 1 : 0)
      .scale(duration: 300.ms, curve: Curves.easeInOut)
      .then()
      .shimmer(
        duration: 2000.ms, 
        color: isSelected ? AppColors.primary.withOpacity(0.3) : Colors.transparent,
      );
  }
}

// Premium Home Page
class _PremiumHomePage extends StatefulWidget {
  const _PremiumHomePage();

  @override
  State<_PremiumHomePage> createState() => _PremiumHomePageState();
}

class _PremiumHomePageState extends State<_PremiumHomePage>
    with TickerProviderStateMixin {
  late AnimationController _balanceController;
  late AnimationController _cardController;

  @override
  void initState() {
    super.initState();
    
    _balanceController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _cardController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _balanceController.dispose();
    _cardController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;

    return SafeArea(
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Premium Header
            Row(
              children: [
                // Profile Avatar with Glow
                AnimatedBuilder(
                  animation: _balanceController,
                  builder: (context, child) {
                    return Container(
                      width: isSmallScreen ? 60 : 70,
                      height: isSmallScreen ? 60 : 70,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: AppColors.primaryGradient,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.4 + 0.3 * _balanceController.value),
                            blurRadius: 20 + 15 * _balanceController.value,
                            spreadRadius: 5 + 3 * _balanceController.value,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.person_rounded,
                        color: AppColors.textOnPrimary,
                        size: isSmallScreen ? 30 : 35,
                      ),
                    );
                  },
                ).animate()
                  .fadeIn(duration: 800.ms)
                  .scale(delay: 200.ms, duration: 600.ms),
                
                SizedBox(width: isSmallScreen ? 16 : 20),
                
                // Welcome Text
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Good Morning',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 18 : 22,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textSecondary,
                        ),
                      ).animate()
                        .fadeIn(delay: 400.ms, duration: 800.ms)
                        .slideX(begin: -0.3, delay: 400.ms, duration: 800.ms),
                      
                      const SizedBox(height: 4),
                      
                      ShaderMask(
                        shaderCallback: (bounds) => LinearGradient(
                          colors: AppColors.primaryGradient,
                        ).createShader(bounds),
                        child: Text(
                          'John Doe',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 28 : 36,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                            letterSpacing: 1,
                          ),
                        ),
                      ).animate()
                        .fadeIn(delay: 600.ms, duration: 800.ms)
                        .slideX(begin: -0.3, delay: 600.ms, duration: 800.ms),
                    ],
                  ),
                ),
                
                // Notification Bell with Pulse
                AnimatedBuilder(
                  animation: _balanceController,
                  builder: (context, child) {
                    return Container(
                      width: isSmallScreen ? 60 : 70,
                      height: isSmallScreen ? 60 : 70,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.surface.withOpacity(0.9),
                            AppColors.surfaceVariant.withOpacity(0.7),
                          ],
                        ),
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.3),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.2 + 0.1 * _balanceController.value),
                            blurRadius: 15 + 10 * _balanceController.value,
                            spreadRadius: 3 + 2 * _balanceController.value,
                          ),
                        ],
                      ),
                      child: Stack(
                        children: [
                          Center(
                            child: Icon(
                              Icons.notifications_rounded,
                              color: AppColors.primary,
                              size: isSmallScreen ? 28 : 32,
                            ),
                          ),
                          Positioned(
                            top: 15,
                            right: 15,
                            child: Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: AppColors.neonGradient,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.accent.withOpacity(0.6),
                                    blurRadius: 8,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ).animate()
                  .fadeIn(delay: 500.ms, duration: 800.ms)
                  .scale(delay: 700.ms, duration: 600.ms),
              ],
            ),

            SizedBox(height: isSmallScreen ? 40 : 50),

            // Premium Balance Card - The Masterpiece
            _buildPremiumBalanceCard(isSmallScreen),

            SizedBox(height: isSmallScreen ? 40 : 50),

            // Quick Actions Title
            ShaderMask(
              shaderCallback: (bounds) => LinearGradient(
                colors: AppColors.primaryGradient,
              ).createShader(bounds),
              child: Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: isSmallScreen ? 28 : 36,
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  letterSpacing: 1,
                ),
              ),
            ).animate()
              .fadeIn(delay: 1200.ms, duration: 800.ms)
              .slideX(begin: -0.3, delay: 1200.ms, duration: 800.ms),
            
            SizedBox(height: isSmallScreen ? 25 : 35),
            
            // Premium Action Grid
            _buildPremiumActionGrid(isSmallScreen),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumBalanceCard(bool isSmallScreen) {
    return AnimatedBuilder(
      animation: _balanceController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(isSmallScreen ? 32 : 40),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.surface.withOpacity(0.95),
                AppColors.surfaceVariant.withOpacity(0.9),
                AppColors.surfaceGlass.withOpacity(0.85),
              ],
            ),
            border: Border.all(
              color: AppColors.primary.withOpacity(0.4 + 0.2 * _balanceController.value),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.3 + 0.2 * _balanceController.value),
                blurRadius: 30 + 20 * _balanceController.value,
                spreadRadius: 8 + 5 * _balanceController.value,
              ),
              BoxShadow(
                color: AppColors.accent.withOpacity(0.2 + 0.1 * _balanceController.value),
                blurRadius: 50 + 30 * _balanceController.value,
                spreadRadius: 12 + 8 * _balanceController.value,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.4),
                blurRadius: 25,
                offset: const Offset(0, 15),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ShaderMask(
                    shaderCallback: (bounds) => LinearGradient(
                      colors: AppColors.primaryGradient,
                    ).createShader(bounds),
                    child: Text(
                      'Total Balance',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 20 : 24,
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 1,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: LinearGradient(
                        colors: AppColors.primaryGradient.map((c) => c.withOpacity(0.3)).toList(),
                      ),
                      border: Border.all(
                        color: AppColors.primary.withOpacity(0.5),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Text(
                      'USD',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: isSmallScreen ? 25 : 35),

              // Main Balance with Extreme Glow
              Transform.scale(
                scale: 1 + (_balanceController.value * 0.05),
                child: ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: AppColors.primaryGradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
                  child: Text(
                    '\$24,850.00',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 56 : 72,
                      fontWeight: FontWeight.w900,
                      color: Colors.white,
                      letterSpacing: 2,
                      shadows: [
                        Shadow(
                          color: AppColors.primary.withOpacity(0.8),
                          blurRadius: 30,
                          offset: const Offset(0, 8),
                        ),
                        Shadow(
                          color: AppColors.accent.withOpacity(0.6),
                          blurRadius: 50,
                          offset: const Offset(0, 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SizedBox(height: isSmallScreen ? 25 : 35),

              // Premium Stats Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildPremiumStatItem(
                    icon: Icons.trending_up_rounded,
                    label: 'Today',
                    value: '+\$125.50',
                    color: AppColors.primary,
                    isSmallScreen: isSmallScreen,
                  ),
                  Container(
                    width: 2,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          AppColors.primary.withOpacity(0.5),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                  _buildPremiumStatItem(
                    icon: Icons.calendar_today_rounded,
                    label: 'This Week',
                    value: '+\$890.25',
                    color: AppColors.secondary,
                    isSmallScreen: isSmallScreen,
                  ),
                  Container(
                    width: 2,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          AppColors.primary.withOpacity(0.5),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                  _buildPremiumStatItem(
                    icon: Icons.show_chart_rounded,
                    label: 'Growth',
                    value: '+18.5%',
                    color: AppColors.accent,
                    isSmallScreen: isSmallScreen,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    ).animate()
      .fadeIn(delay: 800.ms, duration: 1000.ms)
      .slideY(begin: 0.4, delay: 800.ms, duration: 1000.ms)
      .then()
      .shimmer(duration: 3000.ms, color: AppColors.primary.withOpacity(0.2));
  }

  Widget _buildPremiumStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isSmallScreen,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [color, color.withOpacity(0.7)],
            ),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.4),
                blurRadius: 15,
                spreadRadius: 3,
              ),
            ],
          ),
          child: Icon(
            icon,
            color: AppColors.textOnPrimary,
            size: isSmallScreen ? 24 : 28,
          ),
        ),
        const SizedBox(height: 12),
        ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: [color, color.withOpacity(0.8)],
          ).createShader(bounds),
          child: Text(
            value,
            style: TextStyle(
              fontSize: isSmallScreen ? 18 : 22,
              fontWeight: FontWeight.w800,
              color: Colors.white,
              letterSpacing: 1,
            ),
          ),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: TextStyle(
            fontSize: isSmallScreen ? 13 : 15,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPremiumActionGrid(bool isSmallScreen) {
    final actions = [
      {
        'icon': Icons.task_alt_rounded,
        'title': 'Complete Tasks',
        'subtitle': '5 tasks available',
        'color': AppColors.primary,
        'gradient': AppColors.primaryGradient,
      },
      {
        'icon': Icons.play_circle_rounded,
        'title': 'Watch Videos',
        'subtitle': 'Earn while watching',
        'color': AppColors.secondary,
        'gradient': AppColors.earningGradient,
      },
      {
        'icon': Icons.games_rounded,
        'title': 'Play Games',
        'subtitle': 'Fun & rewarding',
        'color': AppColors.accent,
        'gradient': AppColors.goldGradient,
      },
      {
        'icon': Icons.people_rounded,
        'title': 'Refer Friends',
        'subtitle': 'Get bonus rewards',
        'color': AppColors.accentSecondary,
        'gradient': AppColors.neonGradient,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: isSmallScreen ? 16 : 20,
        mainAxisSpacing: isSmallScreen ? 16 : 20,
        childAspectRatio: 1.1,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return _buildPremiumActionCard(
          icon: action['icon'] as IconData,
          title: action['title'] as String,
          subtitle: action['subtitle'] as String,
          color: action['color'] as Color,
          gradient: action['gradient'] as List<Color>,
          index: index,
          isSmallScreen: isSmallScreen,
        );
      },
    );
  }

  Widget _buildPremiumActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required List<Color> gradient,
    required int index,
    required bool isSmallScreen,
  }) {
    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        return Container(
          padding: EdgeInsets.all(isSmallScreen ? 20 : 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.surface.withOpacity(0.9),
                AppColors.surfaceVariant.withOpacity(0.8),
                AppColors.surfaceGlass.withOpacity(0.7),
              ],
            ),
            border: Border.all(
              color: color.withOpacity(0.4 + 0.2 * _cardController.value),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.2 + 0.1 * _cardController.value),
                blurRadius: 20 + 10 * _cardController.value,
                spreadRadius: 4 + 2 * _cardController.value,
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: isSmallScreen ? 55 : 65,
                height: isSmallScreen ? 55 : 65,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(colors: gradient),
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.5),
                      blurRadius: 20,
                      spreadRadius: 4,
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: AppColors.textOnPrimary,
                  size: isSmallScreen ? 28 : 32,
                ),
              ),
              const Spacer(),
              ShaderMask(
                shaderCallback: (bounds) => LinearGradient(
                  colors: gradient,
                ).createShader(bounds),
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 18 : 22,
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
              const SizedBox(height: 6),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: isSmallScreen ? 13 : 15,
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    ).animate()
      .fadeIn(delay: (1400 + index * 200).ms, duration: 800.ms)
      .slideY(begin: 0.3, delay: (1400 + index * 200).ms, duration: 800.ms)
      .then()
      .shimmer(duration: 2000.ms, color: color.withOpacity(0.2));
  }
}

// Placeholder Premium Pages
class _PremiumTasksPage extends StatelessWidget {
  const _PremiumTasksPage();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Premium Tasks Page',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }
}

class _PremiumVideosPage extends StatelessWidget {
  const _PremiumVideosPage();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Premium Videos Page',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }
}

class _PremiumGamesPage extends StatelessWidget {
  const _PremiumGamesPage();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Premium Games Page',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }
}

class _PremiumProfilePage extends StatelessWidget {
  const _PremiumProfilePage();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Premium Profile Page',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w700,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }
}
