import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../widgets/task_card.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen>
    with TickerProviderStateMixin {
  int _selectedTab = 0;

  final List<String> _tabs = ['Available', 'Completed', 'Pending'];

  final List<TaskData> _tasks = [
    TaskData(
      id: '1',
      title: 'Complete Daily Survey',
      description: 'Answer 10 questions about your shopping habits',
      reward: 2.50,
      timeRequired: '5 min',
      difficulty: TaskDifficulty.easy,
      category: 'Survey',
      status: TaskStatus.available,
      progress: 0.0,
      icon: Icons.quiz,
      color: AppColors.primary,
    ),
    TaskData(
      id: '2',
      title: 'Download & Try New App',
      description: 'Download FoodieApp and create an account',
      reward: 5.00,
      timeRequired: '10 min',
      difficulty: TaskDifficulty.medium,
      category: 'App Install',
      status: TaskStatus.available,
      progress: 0.0,
      icon: Icons.download,
      color: AppColors.accent,
    ),
    TaskData(
      id: '3',
      title: 'Share on Social Media',
      description: 'Share our app with your friends on Facebook',
      reward: 1.50,
      timeRequired: '2 min',
      difficulty: TaskDifficulty.easy,
      category: 'Social',
      status: TaskStatus.completed,
      progress: 1.0,
      icon: Icons.share,
      color: AppColors.earning,
    ),
    TaskData(
      id: '4',
      title: 'Product Review',
      description: 'Write a detailed review for a product you purchased',
      reward: 7.50,
      timeRequired: '15 min',
      difficulty: TaskDifficulty.hard,
      category: 'Review',
      status: TaskStatus.pending,
      progress: 0.6,
      icon: Icons.rate_review,
      color: AppColors.gold,
    ),
    TaskData(
      id: '5',
      title: 'Watch Advertisement',
      description: 'Watch a 30-second ad about new smartphone features',
      reward: 0.75,
      timeRequired: '1 min',
      difficulty: TaskDifficulty.easy,
      category: 'Advertisement',
      status: TaskStatus.available,
      progress: 0.0,
      icon: Icons.ads_click,
      color: AppColors.info,
    ),
    TaskData(
      id: '6',
      title: 'Complete Profile',
      description: 'Fill out your complete profile information',
      reward: 3.00,
      timeRequired: '8 min',
      difficulty: TaskDifficulty.medium,
      category: 'Profile',
      status: TaskStatus.completed,
      progress: 1.0,
      icon: Icons.person,
      color: AppColors.success,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // Tabs
              _buildTabs(),
              
              // Tasks List
              Expanded(
                child: _buildTasksList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tasks & Rewards',
                style: AppTextStyles.h3.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 600.ms),
              
              const SizedBox(height: 4),
              
              Text(
                'Complete tasks to earn more',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
            ],
          ),
          
          // Today's task earnings
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: AppColors.goldGradient,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.gold.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.task_alt,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '\$8.75',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ).animate().scale(duration: 600.ms, delay: 400.ms),
        ],
      ),
    );
  }

  Widget _buildTabs() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: List.generate(_tabs.length, (index) {
          final isSelected = _selectedTab == index;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTab = index;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _tabs[index],
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isSelected ? Colors.white : AppColors.textSecondary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ).animate().scale(duration: 200.ms),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTasksList() {
    final filteredTasks = _getFilteredTasks();

    if (filteredTasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_outlined,
              size: 80,
              color: AppColors.textHint,
            ).animate().scale(duration: 600.ms),
            
            const SizedBox(height: 20),
            
            Text(
              'No tasks available',
              style: AppTextStyles.h5.copyWith(
                color: AppColors.textSecondary,
              ),
            ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
            
            const SizedBox(height: 8),
            
            Text(
              'Check back later for new tasks',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textHint,
              ),
            ).animate().fadeIn(duration: 600.ms, delay: 400.ms),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      itemCount: filteredTasks.length,
      itemBuilder: (context, index) {
        final task = filteredTasks[index];
        return TaskCard(
          task: task,
          onTap: () => _handleTaskTap(task),
        ).animate().slideX(
          duration: 600.ms,
          delay: (index * 100).ms,
        );
      },
    );
  }

  List<TaskData> _getFilteredTasks() {
    switch (_selectedTab) {
      case 0: // Available
        return _tasks.where((task) => task.status == TaskStatus.available).toList();
      case 1: // Completed
        return _tasks.where((task) => task.status == TaskStatus.completed).toList();
      case 2: // Pending
        return _tasks.where((task) => task.status == TaskStatus.pending).toList();
      default:
        return _tasks;
    }
  }

  void _handleTaskTap(TaskData task) {
    if (task.status == TaskStatus.available) {
      _startTask(task);
    } else if (task.status == TaskStatus.pending) {
      _continueTask(task);
    } else {
      _showTaskDetails(task);
    }
  }

  void _startTask(TaskData task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Start Task',
          style: AppTextStyles.h5,
        ),
        content: Text(
          'Start "${task.title}" and earn \$${task.reward.toStringAsFixed(2)}?',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                task.status = TaskStatus.pending;
                task.progress = 0.3;
              });
            },
            child: Text(
              'Start',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _continueTask(TaskData task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Continue Task',
          style: AppTextStyles.h5,
        ),
        content: Text(
          'Continue with "${task.title}"? Progress: ${(task.progress * 100).toInt()}%',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Later',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                task.status = TaskStatus.completed;
                task.progress = 1.0;
              });
              _showEarningDialog(task.reward);
            },
            child: Text(
              'Complete',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _showTaskDetails(TaskData task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Task Completed',
          style: AppTextStyles.h5,
        ),
        content: Text(
          'You have already completed "${task.title}" and earned \$${task.reward.toStringAsFixed(2)}',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'OK',
              style: AppTextStyles.buttonMedium,
            ),
          ),
        ],
      ),
    );
  }

  void _showEarningDialog(double amount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppColors.goldGradient,
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.task_alt,
                color: Colors.white,
                size: 40,
              ),
            ).animate().scale(duration: 600.ms, curve: Curves.elasticOut),
            
            const SizedBox(height: 20),
            
            Text(
              'Task Completed!',
              style: AppTextStyles.h5.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'You earned \$${amount.toStringAsFixed(2)}',
              style: AppTextStyles.h4.copyWith(
                color: AppColors.gold,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Continue',
                style: AppTextStyles.buttonMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TaskData {
  final String id;
  final String title;
  final String description;
  final double reward;
  final String timeRequired;
  final TaskDifficulty difficulty;
  final String category;
  TaskStatus status;
  double progress;
  final IconData icon;
  final Color color;

  TaskData({
    required this.id,
    required this.title,
    required this.description,
    required this.reward,
    required this.timeRequired,
    required this.difficulty,
    required this.category,
    required this.status,
    required this.progress,
    required this.icon,
    required this.color,
  });
}

enum TaskStatus { available, pending, completed }
enum TaskDifficulty { easy, medium, hard }
