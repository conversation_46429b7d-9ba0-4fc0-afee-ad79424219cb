import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _progressController;
  int _selectedCategory = 0;

  final List<String> _categories = [
    'All Tasks',
    'Daily',
    'Weekly',
    'Special',
  ];

  final List<TaskModel> _tasks = [
    TaskModel(
      id: '1',
      title: 'Complete Daily Survey',
      description: 'Answer 10 questions about your shopping habits',
      reward: 25.0,
      progress: 0.8,
      category: 'Daily',
      difficulty: 'Easy',
      timeEstimate: '5 min',
      icon: Icons.quiz_rounded,
      color: Color(0xFF00FF88),
    ),
    TaskModel(
      id: '2',
      title: 'Watch Product Demo',
      description: 'Watch a 3-minute video about new tech products',
      reward: 15.0,
      progress: 0.0,
      category: 'Daily',
      difficulty: 'Easy',
      timeEstimate: '3 min',
      icon: Icons.play_circle_rounded,
      color: Color(0xFF10B981),
    ),
    TaskModel(
      id: '3',
      title: 'Social Media Engagement',
      description: 'Like and share 5 posts on social media',
      reward: 20.0,
      progress: 0.6,
      category: 'Daily',
      difficulty: 'Easy',
      timeEstimate: '10 min',
      icon: Icons.thumb_up_rounded,
      color: Color(0xFF22C55E),
    ),
    TaskModel(
      id: '4',
      title: 'Weekly Challenge',
      description: 'Complete 20 tasks this week for bonus reward',
      reward: 100.0,
      progress: 0.35,
      category: 'Weekly',
      difficulty: 'Medium',
      timeEstimate: '1 week',
      icon: Icons.emoji_events_rounded,
      color: Color(0xFF34D399),
    ),
    TaskModel(
      id: '5',
      title: 'Premium Survey',
      description: 'Detailed market research survey (50 questions)',
      reward: 75.0,
      progress: 0.0,
      category: 'Special',
      difficulty: 'Hard',
      timeEstimate: '20 min',
      icon: Icons.star_rounded,
      color: Color(0xFF4ADE80),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    Future.delayed(const Duration(milliseconds: 300), () {
      _progressController.forward();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header Section
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                child: Column(
                  children: [
                    // Title and Stats
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Tasks',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 28 : 32,
                                fontWeight: FontWeight.w900,
                                color: AppColors.textPrimary,
                              ),
                            ).animate()
                              .fadeIn(duration: 600.ms)
                              .slideX(begin: -0.3, duration: 600.ms),
                            
                            const SizedBox(height: 8),
                            
                            Text(
                              'Complete tasks to earn rewards',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 14 : 16,
                                color: AppColors.textSecondary,
                              ),
                            ).animate()
                              .fadeIn(delay: 200.ms, duration: 600.ms)
                              .slideX(begin: -0.3, delay: 200.ms, duration: 600.ms),
                          ],
                        ),
                        
                        // Today's Earnings
                        Container(
                          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            gradient: LinearGradient(
                              colors: AppColors.primaryGradient.map((c) => c.withOpacity(0.2)).toList(),
                            ),
                            border: Border.all(
                              color: AppColors.primary.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              Text(
                                'Today',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 12 : 13,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              const SizedBox(height: 4),
                              AnimatedBuilder(
                                animation: _pulseController,
                                builder: (context, child) {
                                  return Transform.scale(
                                    scale: 1 + (_pulseController.value * 0.05),
                                    child: Text(
                                      '\$85.00',
                                      style: TextStyle(
                                        fontSize: isSmallScreen ? 18 : 20,
                                        fontWeight: FontWeight.w800,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ).animate()
                          .fadeIn(delay: 400.ms, duration: 600.ms)
                          .scale(delay: 600.ms, duration: 400.ms),
                      ],
                    ),
                    
                    SizedBox(height: isSmallScreen ? 25 : 30),
                    
                    // Progress Card
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(isSmallScreen ? 20 : 24),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.surface.withOpacity(0.9),
                            AppColors.surfaceVariant.withOpacity(0.8),
                          ],
                        ),
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.2),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.1),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Daily Progress',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 16 : 18,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              Text(
                                '3/5 Tasks',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 14 : 16,
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          
                          SizedBox(height: isSmallScreen ? 15 : 20),
                          
                          // Progress Bar
                          Container(
                            width: double.infinity,
                            height: 8,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: AppColors.surfaceVariant,
                            ),
                            child: AnimatedBuilder(
                              animation: _progressController,
                              builder: (context, child) {
                                return Stack(
                                  children: [
                                    Container(
                                      width: double.infinity * 0.6 * _progressController.value,
                                      height: 8,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        gradient: LinearGradient(
                                          colors: AppColors.primaryGradient,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.primary.withOpacity(0.4),
                                            blurRadius: 8,
                                            spreadRadius: 1,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                          
                          SizedBox(height: isSmallScreen ? 15 : 20),
                          
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '60% Complete',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 14 : 15,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              Text(
                                '2 tasks remaining',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 14 : 15,
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ).animate()
                      .fadeIn(delay: 600.ms, duration: 800.ms)
                      .slideY(begin: 0.3, delay: 600.ms, duration: 800.ms),
                    
                    SizedBox(height: isSmallScreen ? 25 : 30),
                    
                    // Category Tabs
                    Container(
                      height: 50,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final isSelected = _selectedCategory == index;
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedCategory = index;
                              });
                            },
                            child: Container(
                              margin: const EdgeInsets.only(right: 12),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25),
                                gradient: isSelected
                                    ? LinearGradient(colors: AppColors.primaryGradient)
                                    : null,
                                color: isSelected ? null : AppColors.surface,
                                border: Border.all(
                                  color: isSelected
                                      ? Colors.transparent
                                      : AppColors.primary.withOpacity(0.2),
                                  width: 1,
                                ),
                                boxShadow: isSelected
                                    ? [
                                        BoxShadow(
                                          color: AppColors.primary.withOpacity(0.3),
                                          blurRadius: 10,
                                          spreadRadius: 2,
                                        ),
                                      ]
                                    : null,
                              ),
                              child: Text(
                                _categories[index],
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? AppColors.textOnPrimary
                                      : AppColors.textSecondary,
                                ),
                              ),
                            ),
                          ).animate(target: isSelected ? 1 : 0)
                            .scale(duration: 200.ms, curve: Curves.easeInOut);
                        },
                      ),
                    ).animate()
                      .fadeIn(delay: 800.ms, duration: 600.ms)
                      .slideX(begin: -0.3, delay: 800.ms, duration: 600.ms),
                  ],
                ),
              ),
              
              // Tasks List
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
                  itemCount: _getFilteredTasks().length,
                  itemBuilder: (context, index) {
                    final task = _getFilteredTasks()[index];
                    return _buildTaskCard(task, index, isSmallScreen);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TaskModel> _getFilteredTasks() {
    if (_selectedCategory == 0) return _tasks;
    return _tasks.where((task) => task.category == _categories[_selectedCategory]).toList();
  }

  Widget _buildTaskCard(TaskModel task, int index, bool isSmallScreen) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.surface.withOpacity(0.9),
            AppColors.surfaceVariant.withOpacity(0.8),
          ],
        ),
        border: Border.all(
          color: task.color.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: task.color.withOpacity(0.1),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            children: [
              Container(
                width: isSmallScreen ? 50 : 55,
                height: isSmallScreen ? 50 : 55,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [task.color, task.color.withOpacity(0.7)],
                  ),
                ),
                child: Icon(
                  task.icon,
                  color: AppColors.textOnPrimary,
                  size: isSmallScreen ? 24 : 26,
                ),
              ),

              SizedBox(width: isSmallScreen ? 12 : 15),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            task.title,
                            style: TextStyle(
                              fontSize: isSmallScreen ? 16 : 18,
                              fontWeight: FontWeight.w700,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: task.color.withOpacity(0.2),
                          ),
                          child: Text(
                            task.difficulty,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: task.color,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    Text(
                      task.description,
                      style: TextStyle(
                        fontSize: isSmallScreen ? 13 : 14,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: isSmallScreen ? 15 : 20),

          // Progress Bar (if task is started)
          if (task.progress > 0) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Progress',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${(task.progress * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 12,
                    color: task.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Container(
              width: double.infinity,
              height: 6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: AppColors.surfaceVariant,
              ),
              child: Stack(
                children: [
                  Container(
                    width: double.infinity * task.progress,
                    height: 6,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      gradient: LinearGradient(
                        colors: [task.color, task.color.withOpacity(0.7)],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: isSmallScreen ? 15 : 20),
          ],

          // Bottom Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.access_time_rounded,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    task.timeEstimate,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),

              Row(
                children: [
                  Text(
                    '\$${task.reward.toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 18 : 20,
                      fontWeight: FontWeight.w800,
                      color: task.color,
                    ),
                  ),

                  const SizedBox(width: 12),

                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        colors: [task.color, task.color.withOpacity(0.8)],
                      ),
                    ),
                    child: Text(
                      task.progress > 0 ? 'Continue' : 'Start',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textOnPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ).animate()
      .fadeIn(delay: (1000 + index * 100).ms, duration: 600.ms)
      .slideY(begin: 0.3, delay: (1000 + index * 100).ms, duration: 600.ms);
  }
}

class TaskModel {
  final String id;
  final String title;
  final String description;
  final double reward;
  final double progress;
  final String category;
  final String difficulty;
  final String timeEstimate;
  final IconData icon;
  final Color color;

  TaskModel({
    required this.id,
    required this.title,
    required this.description,
    required this.reward,
    required this.progress,
    required this.category,
    required this.difficulty,
    required this.timeEstimate,
    required this.icon,
    required this.color,
  });
}
