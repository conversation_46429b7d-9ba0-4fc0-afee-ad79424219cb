import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';
import '../tasks/tasks_screen.dart';
import '../videos/videos_screen.dart';
import '../games/games_screen.dart';
import '../profile/profile_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _backgroundController;
  late PageController _pageController;
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const _HomePage(),
    const TasksScreen(),
    const VideosScreen(),
    const GamesScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
    
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat();
    
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _backgroundController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: Stack(
          children: [
            // Animated Background
            ...List.generate(12, (index) {
              return AnimatedBuilder(
                animation: _backgroundController,
                builder: (context, child) {
                  final progress = (_backgroundController.value + index * 0.1) % 1.0;
                  final x = MediaQuery.of(context).size.width * (0.1 + 0.8 * math.sin(index * 0.7));
                  final y = MediaQuery.of(context).size.height * (1.2 - progress * 1.4);
                  final opacity = (1 - progress) * 0.1;
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Container(
                      width: 4 + (index % 3) * 2,
                      height: 4 + (index % 3) * 2,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            AppColors.primary.withOpacity(opacity),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Main Content
            PageView(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _selectedIndex = index;
                });
              },
              children: _pages,
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNav(),
    );
  }

  Widget _buildBottomNav() {
    return Container(
      height: 85,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.surface.withOpacity(0.95),
            AppColors.surfaceVariant.withOpacity(0.98),
          ],
        ),
        border: Border(
          top: BorderSide(
            color: AppColors.primary.withOpacity(0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(Icons.home_rounded, 'Home', 0),
            _buildNavItem(Icons.task_alt_rounded, 'Tasks', 1),
            _buildNavItem(Icons.play_circle_rounded, 'Videos', 2),
            _buildNavItem(Icons.games_rounded, 'Games', 3),
            _buildNavItem(Icons.person_rounded, 'Profile', 4),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, int index) {
    final isSelected = _selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        _pageController.animateToPage(
          index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: isSelected
              ? LinearGradient(
                  colors: AppColors.primaryGradient.map((c) => c.withOpacity(0.2)).toList(),
                )
              : null,
          border: isSelected
              ? Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 1,
                )
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                icon,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                size: isSelected ? 26 : 24,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    ).animate(target: isSelected ? 1 : 0)
      .scale(duration: 200.ms, curve: Curves.easeInOut);
  }
}

class _HomePage extends StatefulWidget {
  const _HomePage();

  @override
  State<_HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<_HomePage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    // Start animations
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;

    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Row(
              children: [
                // Profile Avatar
                Container(
                  width: isSmallScreen ? 50 : 55,
                  height: isSmallScreen ? 50 : 55,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: AppColors.primaryGradient,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.3),
                        blurRadius: 15,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.person_rounded,
                    color: AppColors.textOnPrimary,
                    size: isSmallScreen ? 25 : 28,
                  ),
                ).animate()
                  .fadeIn(duration: 600.ms)
                  .scale(delay: 200.ms, duration: 400.ms),
                
                SizedBox(width: isSmallScreen ? 15 : 18),
                
                // Welcome Text
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Good Morning',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 16 : 18,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textSecondary,
                        ),
                      ).animate()
                        .fadeIn(delay: 300.ms, duration: 600.ms)
                        .slideX(begin: -0.3, delay: 300.ms, duration: 600.ms),
                      
                      Text(
                        'John Doe',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 24 : 28,
                          fontWeight: FontWeight.w800,
                          color: AppColors.textPrimary,
                        ),
                      ).animate()
                        .fadeIn(delay: 500.ms, duration: 600.ms)
                        .slideX(begin: -0.3, delay: 500.ms, duration: 600.ms),
                    ],
                  ),
                ),
                
                // Notification Bell
                Container(
                  width: isSmallScreen ? 50 : 55,
                  height: isSmallScreen ? 50 : 55,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.surface,
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.1),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.notifications_rounded,
                          color: AppColors.primary,
                          size: isSmallScreen ? 24 : 26,
                        ),
                      ),
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.accent,
                          ),
                        ),
                      ),
                    ],
                  ),
                ).animate()
                  .fadeIn(delay: 400.ms, duration: 600.ms)
                  .scale(delay: 600.ms, duration: 400.ms),
              ],
            ),

            SizedBox(height: isSmallScreen ? 30 : 35),

            // Balance Card - The Crown Jewel
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(isSmallScreen ? 24 : 30),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.surface.withOpacity(0.9),
                    AppColors.surfaceVariant.withOpacity(0.8),
                  ],
                ),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.2),
                    blurRadius: 25,
                    spreadRadius: 5,
                  ),
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.1),
                    blurRadius: 50,
                    spreadRadius: 10,
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Total Balance',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 16 : 18,
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: LinearGradient(
                            colors: AppColors.primaryGradient.map((c) => c.withOpacity(0.2)).toList(),
                          ),
                          border: Border.all(
                            color: AppColors.primary.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'USD',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: isSmallScreen ? 15 : 20),
                  
                  // Main Balance with Pulse Animation
                  AnimatedBuilder(
                    animation: _pulseController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: 1 + (_pulseController.value * 0.03),
                        child: ShaderMask(
                          shaderCallback: (bounds) => LinearGradient(
                            colors: AppColors.primaryGradient,
                          ).createShader(bounds),
                          child: Text(
                            '\$24,850.00',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 42 : 52,
                              fontWeight: FontWeight.w900,
                              color: Colors.white,
                              letterSpacing: 1,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  
                  SizedBox(height: isSmallScreen ? 15 : 20),
                  
                  // Stats Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        icon: Icons.trending_up_rounded,
                        label: 'Today',
                        value: '+\$125.50',
                        color: AppColors.primary,
                        isSmallScreen: isSmallScreen,
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color: AppColors.primary.withOpacity(0.2),
                      ),
                      _buildStatItem(
                        icon: Icons.calendar_today_rounded,
                        label: 'This Week',
                        value: '+\$890.25',
                        color: AppColors.secondary,
                        isSmallScreen: isSmallScreen,
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color: AppColors.primary.withOpacity(0.2),
                      ),
                      _buildStatItem(
                        icon: Icons.show_chart_rounded,
                        label: 'Growth',
                        value: '+18.5%',
                        color: AppColors.accent,
                        isSmallScreen: isSmallScreen,
                      ),
                    ],
                  ),
                ],
              ),
            ).animate()
              .fadeIn(delay: 700.ms, duration: 800.ms)
              .slideY(begin: 0.3, delay: 700.ms, duration: 800.ms),

            SizedBox(height: isSmallScreen ? 30 : 35),

            // Quick Actions Grid
            Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: isSmallScreen ? 20 : 24,
                fontWeight: FontWeight.w700,
                color: AppColors.textPrimary,
              ),
            ).animate()
              .fadeIn(delay: 1000.ms, duration: 600.ms)
              .slideX(begin: -0.3, delay: 1000.ms, duration: 600.ms),
            
            SizedBox(height: isSmallScreen ? 20 : 25),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: isSmallScreen ? 15 : 20,
              mainAxisSpacing: isSmallScreen ? 15 : 20,
              childAspectRatio: 1.3,
              children: [
                _buildActionCard(
                  icon: Icons.task_alt_rounded,
                  title: 'Complete Tasks',
                  subtitle: '5 tasks available',
                  color: AppColors.primary,
                  index: 0,
                  isSmallScreen: isSmallScreen,
                ),
                _buildActionCard(
                  icon: Icons.play_circle_rounded,
                  title: 'Watch Videos',
                  subtitle: 'Earn while watching',
                  color: AppColors.secondary,
                  index: 1,
                  isSmallScreen: isSmallScreen,
                ),
                _buildActionCard(
                  icon: Icons.games_rounded,
                  title: 'Play Games',
                  subtitle: 'Fun & rewarding',
                  color: AppColors.accent,
                  index: 2,
                  isSmallScreen: isSmallScreen,
                ),
                _buildActionCard(
                  icon: Icons.people_rounded,
                  title: 'Refer Friends',
                  subtitle: 'Get bonus rewards',
                  color: AppColors.accentSecondary,
                  index: 3,
                  isSmallScreen: isSmallScreen,
                ),
              ],
            ),

            SizedBox(height: isSmallScreen ? 30 : 35),

            // Recent Activity
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 20 : 24,
                    fontWeight: FontWeight.w700,
                    color: AppColors.textPrimary,
                  ),
                ),
                TextButton(
                  onPressed: () {},
                  child: Text(
                    'View All',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ).animate()
              .fadeIn(delay: 1600.ms, duration: 600.ms)
              .slideX(begin: -0.3, delay: 1600.ms, duration: 600.ms),
            
            SizedBox(height: isSmallScreen ? 15 : 20),
            
            // Activity List
            ...List.generate(3, (index) {
              return _buildActivityItem(
                icon: [Icons.task_alt, Icons.play_circle, Icons.games][index],
                title: ['Task Completed', 'Video Watched', 'Game Played'][index],
                subtitle: ['Daily Survey #${index + 1}', 'Marketing Video', 'Puzzle Game'][index],
                amount: '+\$${[25, 15, 10][index]}.00',
                time: '${index + 1}h ago',
                color: [AppColors.primary, AppColors.secondary, AppColors.accent][index],
                index: index,
                isSmallScreen: isSmallScreen,
              );
            }),

            SizedBox(height: isSmallScreen ? 20 : 30),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isSmallScreen,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: isSmallScreen ? 20 : 22,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: isSmallScreen ? 12 : 13,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required int index,
    required bool isSmallScreen,
  }) {
    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.surface.withOpacity(0.8),
            AppColors.surfaceVariant.withOpacity(0.6),
          ],
        ),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: isSmallScreen ? 45 : 50,
            height: isSmallScreen ? 45 : 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [color, color.withOpacity(0.7)],
              ),
            ),
            child: Icon(
              icon,
              color: AppColors.textOnPrimary,
              size: isSmallScreen ? 22 : 25,
            ),
          ),
          const Spacer(),
          Text(
            title,
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w700,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: isSmallScreen ? 12 : 13,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    ).animate()
      .fadeIn(delay: (1200 + index * 100).ms, duration: 600.ms)
      .slideY(begin: 0.3, delay: (1200 + index * 100).ms, duration: 600.ms);
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String amount,
    required String time,
    required Color color,
    required int index,
    required bool isSmallScreen,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppColors.surface.withOpacity(0.8),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: isSmallScreen ? 45 : 50,
            height: isSmallScreen ? 45 : 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [color.withOpacity(0.2), color.withOpacity(0.1)],
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: isSmallScreen ? 22 : 24,
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16 : 17,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 13 : 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                amount,
                style: TextStyle(
                  fontSize: isSmallScreen ? 16 : 17,
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                time,
                style: TextStyle(
                  fontSize: isSmallScreen ? 12 : 13,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate()
      .fadeIn(delay: (1800 + index * 100).ms, duration: 600.ms)
      .slideX(begin: 0.3, delay: (1800 + index * 100).ms, duration: 600.ms);
  }
