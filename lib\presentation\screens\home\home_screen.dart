import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/constants/app_colors.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    // Start slide animation
    _slideController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 400;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header Section
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                child: Row(
                  children: [
                    // Profile Avatar
                    Container(
                      width: isSmallScreen ? 45 : 50,
                      height: isSmallScreen ? 45 : 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: AppColors.primaryGradient,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 15,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.person_rounded,
                        color: AppColors.textOnPrimary,
                        size: isSmallScreen ? 22 : 25,
                      ),
                    ).animate()
                      .fadeIn(duration: 600.ms)
                      .scale(delay: 200.ms, duration: 400.ms),
                    
                    SizedBox(width: isSmallScreen ? 12 : 15),
                    
                    // Welcome Text
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 14 : 16,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textSecondary,
                            ),
                          ).animate()
                            .fadeIn(delay: 300.ms, duration: 600.ms)
                            .slideX(begin: -0.3, delay: 300.ms, duration: 600.ms),
                          
                          Text(
                            'John Doe',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 20 : 24,
                              fontWeight: FontWeight.w700,
                              color: AppColors.textPrimary,
                            ),
                          ).animate()
                            .fadeIn(delay: 500.ms, duration: 600.ms)
                            .slideX(begin: -0.3, delay: 500.ms, duration: 600.ms),
                        ],
                      ),
                    ),
                    
                    // Notification Icon
                    Container(
                      width: isSmallScreen ? 45 : 50,
                      height: isSmallScreen ? 45 : 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.surface,
                        border: Border.all(
                          color: AppColors.primary.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.notifications_outlined,
                        color: AppColors.primary,
                        size: isSmallScreen ? 22 : 25,
                      ),
                    ).animate()
                      .fadeIn(delay: 400.ms, duration: 600.ms)
                      .scale(delay: 600.ms, duration: 400.ms),
                  ],
                ),
              ),

              // Balance Card
              Padding(
                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(isSmallScreen ? 20 : 25),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.surface,
                        AppColors.surfaceVariant,
                      ],
                    ),
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.1),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Total Balance',
                        style: TextStyle(
                          fontSize: isSmallScreen ? 14 : 16,
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      
                      SizedBox(height: isSmallScreen ? 8 : 10),
                      
                      AnimatedBuilder(
                        animation: _pulseController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: 1 + (_pulseController.value * 0.02),
                            child: ShaderMask(
                              shaderCallback: (bounds) => LinearGradient(
                                colors: AppColors.primaryGradient,
                              ).createShader(bounds),
                              child: Text(
                                '\$12,450.00',
                                style: TextStyle(
                                  fontSize: isSmallScreen ? 32 : 40,
                                  fontWeight: FontWeight.w900,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      
                      SizedBox(height: isSmallScreen ? 8 : 10),
                      
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.trending_up_rounded,
                            color: AppColors.primary,
                            size: isSmallScreen ? 16 : 18,
                          ),
                          const SizedBox(width: 5),
                          Text(
                            '+15.2% this week',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 12 : 14,
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ).animate()
                .fadeIn(delay: 700.ms, duration: 800.ms)
                .slideY(begin: 0.3, delay: 700.ms, duration: 800.ms),

              SizedBox(height: isSmallScreen ? 25 : 30),

              // Quick Actions
              Padding(
                padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
                child: Row(
                  children: [
                    _buildQuickAction(
                      icon: Icons.add_circle_outline_rounded,
                      label: 'Earn',
                      color: AppColors.primary,
                      index: 0,
                    ),
                    SizedBox(width: isSmallScreen ? 12 : 15),
                    _buildQuickAction(
                      icon: Icons.send_rounded,
                      label: 'Send',
                      color: AppColors.secondary,
                      index: 1,
                    ),
                    SizedBox(width: isSmallScreen ? 12 : 15),
                    _buildQuickAction(
                      icon: Icons.download_rounded,
                      label: 'Receive',
                      color: AppColors.accent,
                      index: 2,
                    ),
                    SizedBox(width: isSmallScreen ? 12 : 15),
                    _buildQuickAction(
                      icon: Icons.analytics_rounded,
                      label: 'Stats',
                      color: AppColors.accentSecondary,
                      index: 3,
                    ),
                  ],
                ),
              ),

              SizedBox(height: isSmallScreen ? 25 : 30),

              // Tasks Section
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    color: AppColors.surface,
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                        child: Row(
                          children: [
                            Text(
                              'Active Tasks',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 18 : 20,
                                fontWeight: FontWeight.w700,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(15),
                                gradient: LinearGradient(
                                  colors: AppColors.primaryGradient,
                                ),
                              ),
                              child: Text(
                                '5 Active',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textOnPrimary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ).animate()
                        .fadeIn(delay: 1200.ms, duration: 600.ms)
                        .slideY(begin: 0.3, delay: 1200.ms, duration: 600.ms),
                      
                      Expanded(
                        child: ListView.builder(
                          padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 16 : 20),
                          itemCount: 5,
                          itemBuilder: (context, index) {
                            return _buildTaskCard(index, isSmallScreen);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate()
                .fadeIn(delay: 1000.ms, duration: 800.ms)
                .slideY(begin: 0.5, delay: 1000.ms, duration: 800.ms),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNav(isSmallScreen),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required Color color,
    required int index,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          color: AppColors.surface,
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    ).animate()
      .fadeIn(delay: (900 + index * 100).ms, duration: 600.ms)
      .slideY(begin: 0.3, delay: (900 + index * 100).ms, duration: 600.ms);
  }

  Widget _buildTaskCard(int index, bool isSmallScreen) {
    final colors = [
      AppColors.primary,
      AppColors.secondary,
      AppColors.accent,
      AppColors.accentSecondary,
      AppColors.accentTertiary,
    ];
    final color = colors[index % colors.length];
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: AppColors.surfaceVariant,
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: isSmallScreen ? 45 : 50,
            height: isSmallScreen ? 45 : 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [color, color.withOpacity(0.7)],
              ),
            ),
            child: Icon(
              Icons.task_alt_rounded,
              color: AppColors.textOnPrimary,
              size: isSmallScreen ? 22 : 25,
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Task ${index + 1}',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'Complete daily earning tasks',
                  style: TextStyle(
                    fontSize: isSmallScreen ? 13 : 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '+\$${(index + 1) * 50}',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ],
      ),
    ).animate()
      .fadeIn(delay: (1400 + index * 100).ms, duration: 600.ms)
      .slideX(begin: 0.3, delay: (1400 + index * 100).ms, duration: 600.ms);
  }

  Widget _buildBottomNav(bool isSmallScreen) {
    return Container(
      height: isSmallScreen ? 70 : 80,
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          top: BorderSide(
            color: AppColors.primary.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(Icons.home_rounded, 'Home', 0, isSmallScreen),
          _buildNavItem(Icons.task_alt_rounded, 'Tasks', 1, isSmallScreen),
          _buildNavItem(Icons.analytics_rounded, 'Stats', 2, isSmallScreen),
          _buildNavItem(Icons.person_rounded, 'Profile', 3, isSmallScreen),
        ],
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, int index, bool isSmallScreen) {
    final isSelected = _selectedIndex == index;
    final color = isSelected ? AppColors.primary : AppColors.textSecondary;
    
    return GestureDetector(
      onTap: () => setState(() => _selectedIndex = index),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: isSmallScreen ? 22 : 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: isSmallScreen ? 10 : 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
