import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_dimensions.dart';
import '../../../core/constants/app_text_styles.dart';
import '../../../core/utils/glassmorphism_utils.dart';
import '../../../core/utils/effects_utils.dart';
import '../../widgets/earning_card.dart';
import '../../widgets/quick_action_card.dart';
import '../../widgets/recent_activity_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _balanceAnimationController;
  late AnimationController _backgroundController;
  late AnimationController _floatingController;
  late AnimationController _pulseController;

  late Animation<double> _balanceAnimation;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _floatingAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Balance animation
    _balanceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );
    _balanceAnimation = Tween<double>(
      begin: 0.0,
      end: 1247.50,
    ).animate(CurvedAnimation(
      parent: _balanceAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Background animation
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    // Floating animation
    _floatingController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat(reverse: true);

    // Pulse animation
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_backgroundController);

    _floatingAnimation = Tween<double>(
      begin: -8.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    Future.delayed(const Duration(milliseconds: 800), () {
      _balanceAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _balanceAnimationController.dispose();
    _backgroundController.dispose();
    _floatingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth < 360;
    final isTablet = screenWidth > 600;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: isSmallScreen ? 16 : (isTablet ? 32 : 20),
                  vertical: isSmallScreen ? 16 : 20,
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                    maxWidth: isTablet ? 800 : double.infinity,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Section
                      _buildHeader(context),

                      SizedBox(height: isSmallScreen ? 20 : 30),

                      // Balance Card
                      _buildBalanceCard(context),

                      SizedBox(height: isSmallScreen ? 20 : 30),

                      // Quick Actions
                      _buildQuickActions(),

                      SizedBox(height: isSmallScreen ? 20 : 30),

                      // Today's Earnings
                      _buildTodaysEarnings(),

                      SizedBox(height: isSmallScreen ? 20 : 30),

                      // Recent Activities
                      _buildRecentActivities(),

                      // Bottom padding for navigation
                      SizedBox(height: isSmallScreen ? 80 : 100),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;
    final buttonSize = isSmallScreen ? 40.0 : 48.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  'Good Morning! 👋',
                  style: AppTextStyles.h5.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: isSmallScreen ? 14 : 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ).animate().fadeIn(duration: 600.ms),
              ),

              SizedBox(height: isSmallScreen ? 2 : 4),

              FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Text(
                  'John Doe',
                  style: AppTextStyles.h3.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: isSmallScreen ? 20 : 24,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ).animate().fadeIn(duration: 600.ms, delay: 200.ms),
              ),
            ],
          ),
        ),

        SizedBox(width: isSmallScreen ? 8 : 16),

        // Notification & Profile
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.cardShadow.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: IconButton(
                onPressed: () {},
                padding: EdgeInsets.zero,
                icon: Icon(
                  Icons.notifications_outlined,
                  color: AppColors.textSecondary,
                  size: isSmallScreen ? 18 : 20,
                ),
              ),
            ).animate().scale(duration: 400.ms, delay: 400.ms),

            SizedBox(width: isSmallScreen ? 8 : 12),

            Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppColors.primaryGradient,
                ),
                borderRadius: BorderRadius.circular(isSmallScreen ? 10 : 12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.person,
                color: Colors.white,
                size: isSmallScreen ? 18 : 20,
              ),
            ).animate().scale(duration: 400.ms, delay: 500.ms),
          ],
        ),
      ],
    );
  }

  Widget _buildBalanceCard(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isSmallScreen ? 20 : 24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: AppColors.primaryGradient,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Balance',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              Icon(
                Icons.visibility_outlined,
                color: Colors.white.withOpacity(0.9),
                size: 20,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Animated Balance
          AnimatedBuilder(
            animation: _balanceAnimation,
            builder: (context, child) {
              return Text(
                '\$${_balanceAnimation.value.toStringAsFixed(2)}',
                style: AppTextStyles.h1.copyWith(
                  color: Colors.white,
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
          
          const SizedBox(height: 8),
          
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: AppColors.earning,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '+12.5% from last month',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.earning,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ).animate().fadeIn(duration: 600.ms, delay: 1000.ms),
          
          const SizedBox(height: 20),
          
          // Withdraw Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Withdraw',
                style: AppTextStyles.buttonMedium.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ).animate().slideY(duration: 600.ms, delay: 1200.ms),
        ],
      ),
    ).animate().slideY(duration: 800.ms, delay: 300.ms);
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTextStyles.h5.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 600.ms),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: QuickActionCard(
                icon: Icons.play_circle_filled,
                title: 'Watch Videos',
                subtitle: 'Earn \$0.50 per video',
                color: AppColors.primary,
                onTap: () {},
              ).animate().slideX(duration: 600.ms, delay: 700.ms),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: QuickActionCard(
                icon: Icons.task_alt,
                title: 'Complete Tasks',
                subtitle: 'Up to \$5.00 per task',
                color: AppColors.accent,
                onTap: () {},
              ).animate().slideX(duration: 600.ms, delay: 800.ms),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: QuickActionCard(
                icon: Icons.games,
                title: 'Play Games',
                subtitle: 'Earn while playing',
                color: AppColors.earning,
                onTap: () {},
              ).animate().slideX(duration: 600.ms, delay: 900.ms),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: QuickActionCard(
                icon: Icons.share,
                title: 'Refer Friends',
                subtitle: 'Get \$10 per referral',
                color: AppColors.gold,
                onTap: () {},
              ).animate().slideX(duration: 600.ms, delay: 1000.ms),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTodaysEarnings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Today\'s Earnings',
              style: AppTextStyles.h5.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {},
              child: Text(
                'View All',
                style: AppTextStyles.link,
              ),
            ),
          ],
        ).animate().fadeIn(duration: 600.ms, delay: 1100.ms),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: EarningCard(
                title: 'Videos Watched',
                amount: '\$12.50',
                count: '25 videos',
                icon: Icons.play_circle,
                color: AppColors.primary,
              ).animate().slideY(duration: 600.ms, delay: 1200.ms),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: EarningCard(
                title: 'Tasks Completed',
                amount: '\$8.75',
                count: '3 tasks',
                icon: Icons.task_alt,
                color: AppColors.accent,
              ).animate().slideY(duration: 600.ms, delay: 1300.ms),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activities',
          style: AppTextStyles.h5.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 1400.ms),
        
        const SizedBox(height: 16),
        
        Column(
          children: [
            RecentActivityCard(
              title: 'Video Completed',
              subtitle: 'Tech Review - iPhone 15',
              amount: '+\$0.50',
              time: '2 min ago',
              icon: Icons.play_circle,
              color: AppColors.primary,
            ).animate().slideX(duration: 600.ms, delay: 1500.ms),
            
            const SizedBox(height: 12),
            
            RecentActivityCard(
              title: 'Task Completed',
              subtitle: 'Survey about mobile apps',
              amount: '+\$2.50',
              time: '15 min ago',
              icon: Icons.task_alt,
              color: AppColors.accent,
            ).animate().slideX(duration: 600.ms, delay: 1600.ms),
            
            const SizedBox(height: 12),
            
            RecentActivityCard(
              title: 'Game Achievement',
              subtitle: 'Puzzle Master Level 10',
              amount: '+\$1.00',
              time: '1 hour ago',
              icon: Icons.games,
              color: AppColors.earning,
            ).animate().slideX(duration: 600.ms, delay: 1700.ms),
          ],
        ),
      ],
    );
  }
}
