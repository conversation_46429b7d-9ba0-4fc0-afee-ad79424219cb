import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/utils/glassmorphism_utils.dart';

class EarningCard extends StatefulWidget {
  final String title;
  final String amount;
  final String count;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const EarningCard({
    super.key,
    required this.title,
    required this.amount,
    required this.count,
    required this.icon,
    required this.color,
    this.onTap,
  });

  @override
  State<EarningCard> createState() => _EarningCardState();
}

class _EarningCardState extends State<EarningCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 360;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001)
              ..rotateX(_isPressed ? 0.05 : 0.0)
              ..rotateY(_isPressed ? 0.02 : 0.0),
            child: GestureDetector(
              onTapDown: (_) {
                setState(() {
                  _isPressed = true;
                });
                _animationController.forward();
              },
              onTapUp: (_) {
                setState(() {
                  _isPressed = false;
                });
                _animationController.reverse();
                widget.onTap?.call();
              },
              onTapCancel: () {
                setState(() {
                  _isPressed = false;
                });
                _animationController.reverse();
              },
              child: Container(
                width: double.infinity,
                constraints: BoxConstraints(
                  minHeight: isSmallScreen ? 140 : 160,
                  maxHeight: 200,
                ),
                padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.surface.withOpacity(0.9),
                      AppColors.surface.withOpacity(0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: widget.color.withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: _isPressed
                          ? widget.color.withOpacity(0.3)
                          : widget.color.withOpacity(0.1),
                      blurRadius: _isPressed ? 15 : 25,
                      offset: Offset(0, _isPressed ? 5 : 15),
                      spreadRadius: _isPressed ? 2 : 0,
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(-5, -5),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Icon with 3D effect
                    Container(
                      width: isSmallScreen ? 48 : 56,
                      height: isSmallScreen ? 48 : 56,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            widget.color.withOpacity(0.2),
                            widget.color.withOpacity(0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: widget.color.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                          BoxShadow(
                            color: Colors.white.withOpacity(0.1),
                            blurRadius: 5,
                            offset: const Offset(-2, -2),
                          ),
                        ],
                      ),
                      child: Transform.scale(
                        scale: _isPressed ? 0.9 : 1.0,
                        child: Icon(
                          widget.icon,
                          color: widget.color,
                          size: isSmallScreen ? 24 : 28,
                        ),
                      ),
                    ).animate()
                      .shimmer(duration: 2000.ms, color: widget.color.withOpacity(0.3))
                      .then()
                      .shake(hz: 2, curve: Curves.easeInOut),

                    SizedBox(height: isSmallScreen ? 12 : 16),

                    // Title
                    Flexible(
                      child: Text(
                        widget.title,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                          fontWeight: FontWeight.w500,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    SizedBox(height: isSmallScreen ? 6 : 8),

                    // Amount
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        alignment: Alignment.centerLeft,
                        child: Text(
                          widget.amount,
                          style: AppTextStyles.h5.copyWith(
                            color: widget.color,
                            fontWeight: FontWeight.bold,
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          maxLines: 1,
                        ),
                      ),
                    ),

                    const SizedBox(height: 4),

                    // Count
                    Flexible(
                      child: Text(
                        widget.count,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textHint,
                          fontSize: isSmallScreen ? 10 : 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
