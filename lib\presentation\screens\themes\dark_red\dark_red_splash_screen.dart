import 'package:flutter/material.dart';

class DarkRedSplashScreen extends StatelessWidget {
  const DarkRedSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF8B0000),
              Color(0xFFDC143C),
              Color(0xFF000000),
            ],
          ),
        ),
        child: const Center(
          child: Text(
            'Dark Red Theme',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
