import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:shimmer/shimmer.dart';

class OceanSplashScreen extends StatefulWidget {
  const OceanSplashScreen({super.key});

  @override
  State<OceanSplashScreen> createState() => _OceanSplashScreenState();
}

class _OceanSplashScreenState extends State<OceanSplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _bubbleController;
  late AnimationController _mainController;

  @override
  void initState() {
    super.initState();
    
    _mainController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();

    _bubbleController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    )..repeat();

    _startSplashSequence();
  }

  void _startSplashSequence() async {
    await Future.delayed(const Duration(milliseconds: 500));
    _mainController.forward();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _waveController.dispose();
    _bubbleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB),
              Color(0xFF4682B4),
              Color(0xFF191970),
              Color(0xFF000080),
            ],
          ),
        ),
        child: Stack(
          children: [
            // Animated Waves
            ...List.generate(3, (index) {
              return AnimatedBuilder(
                animation: _waveController,
                builder: (context, child) {
                  return Positioned(
                    bottom: -50 + (index * 30),
                    left: -size.width,
                    child: Transform.translate(
                      offset: Offset(
                        size.width * 2 * (_waveController.value + index * 0.3) % 1.0 - size.width,
                        math.sin(_waveController.value * 2 * math.pi + index) * 20,
                      ),
                      child: Container(
                        width: size.width * 3,
                        height: 100,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.1 - index * 0.03),
                              Colors.white.withOpacity(0.05 - index * 0.02),
                            ],
                          ),
                        ),
                        child: CustomPaint(
                          painter: WavePainter(
                            animation: _waveController.value + index * 0.3,
                            color: Colors.white.withOpacity(0.2 - index * 0.05),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            // Floating Bubbles
            ...List.generate(20, (index) {
              return AnimatedBuilder(
                animation: _bubbleController,
                builder: (context, child) {
                  final progress = (_bubbleController.value + index * 0.1) % 1.0;
                  final x = size.width * (0.1 + 0.8 * math.sin(index * 0.5));
                  final y = size.height * (1.2 - progress * 1.4);
                  final scale = 0.3 + 0.7 * math.sin(progress * 2 * math.pi);
                  final opacity = (1 - progress) * 0.6;
                  
                  return Positioned(
                    left: x,
                    top: y,
                    child: Transform.scale(
                      scale: scale,
                      child: Container(
                        width: 8 + (index % 3) * 4,
                        height: 8 + (index % 3) * 4,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.white.withOpacity(opacity),
                              Colors.white.withOpacity(opacity * 0.5),
                              Colors.transparent,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withOpacity(opacity * 0.5),
                              blurRadius: 5,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            }),

            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30),
                child: Column(
                  children: [
                    const Spacer(flex: 2),
                    
                    // Ocean Logo
                    AnimatedBuilder(
                      animation: _mainController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _mainController.value,
                          child: Opacity(
                            opacity: _mainController.value,
                            child: AnimatedBuilder(
                              animation: _waveController,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(0, math.sin(_waveController.value * 2 * math.pi) * 5),
                                  child: Container(
                                    width: 150,
                                    height: 150,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: const LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          Color(0xFF87CEEB),
                                          Color(0xFF4682B4),
                                          Color(0xFF191970),
                                        ],
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Color(0xFF4682B4).withOpacity(0.6),
                                          blurRadius: 30,
                                          spreadRadius: 5,
                                        ),
                                        BoxShadow(
                                          color: Colors.white.withOpacity(0.3),
                                          blurRadius: 50,
                                          spreadRadius: 10,
                                        ),
                                      ],
                                    ),
                                    child: Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        // Water ripple effect
                                        Container(
                                          width: 120,
                                          height: 120,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: RadialGradient(
                                              colors: [
                                                Colors.white.withOpacity(0.3),
                                                Colors.transparent,
                                              ],
                                            ),
                                          ),
                                        ),
                                        // Main icon
                                        const Icon(
                                          Icons.waves_rounded,
                                          size: 70,
                                          color: Colors.white,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 50),
                    
                    // Ocean Text
                    AnimatedBuilder(
                      animation: _mainController,
                      builder: (context, child) {
                        return Opacity(
                          opacity: _mainController.value,
                          child: Column(
                            children: [
                              ShaderMask(
                                shaderCallback: (bounds) => const LinearGradient(
                                  colors: [
                                    Color(0xFF87CEEB),
                                    Color(0xFF4682B4),
                                    Color(0xFFFFFFFF),
                                  ],
                                ).createShader(bounds),
                                child: AnimatedTextKit(
                                  animatedTexts: [
                                    WavyAnimatedText(
                                      'Easy Money',
                                      textStyle: const TextStyle(
                                        fontSize: 44,
                                        fontWeight: FontWeight.w900,
                                        color: Colors.white,
                                        letterSpacing: 2,
                                      ),
                                      speed: const Duration(milliseconds: 200),
                                    ),
                                  ],
                                  totalRepeatCount: 1,
                                ),
                              ),
                              
                              const SizedBox(height: 15),
                              
                              Shimmer.fromColors(
                                baseColor: Color(0xFF87CEEB),
                                highlightColor: Colors.white,
                                period: const Duration(milliseconds: 2000),
                                child: const Text(
                                  'OCEAN EDITION',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 3,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    
                    const Spacer(flex: 2),
                    
                    // Wave Loading
                    AnimatedBuilder(
                      animation: _waveController,
                      builder: (context, child) {
                        return Column(
                          children: [
                            Container(
                              width: 200,
                              height: 50,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25),
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.white.withOpacity(0.1),
                                    Colors.white.withOpacity(0.2),
                                  ],
                                ),
                              ),
                              child: CustomPaint(
                                painter: LoadingWavePainter(
                                  animation: _waveController.value,
                                  progress: _waveController.value,
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 20),
                            
                            Text(
                              'Diving into Ocean Experience...',
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFF87CEEB),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    
                    const Spacer(flex: 1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WavePainter extends CustomPainter {
  final double animation;
  final Color color;

  WavePainter({required this.animation, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height / 2);

    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height / 2 + 
          math.sin((x / size.width * 2 * math.pi) + (animation * 2 * math.pi)) * 20;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class LoadingWavePainter extends CustomPainter {
  final double animation;
  final double progress;

  LoadingWavePainter({required this.animation, required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = const LinearGradient(
        colors: [Color(0xFF87CEEB), Color(0xFF4682B4)],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveWidth = size.width * progress;
    
    path.moveTo(0, size.height);
    
    for (double x = 0; x <= waveWidth; x += 1) {
      final y = size.height / 2 + 
          math.sin((x / size.width * 4 * math.pi) + (animation * 4 * math.pi)) * 10;
      path.lineTo(x, y);
    }
    
    path.lineTo(waveWidth, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
